if __name__ == "__main__":
    import ctypes, sys, os

    def is_admin():
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    if not is_admin():
        # Re-run the program with admin rights
        ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, " ".join(sys.argv), None, 1)
        sys.exit()
    try:
        import PyInstaller
    except ImportError:
        print("Installiere PyInstaller...")
        import subprocess
        subprocess.check_call(['pip', 'install', 'pyinstaller'])

    import sys
    import subprocess


    source_files = [
        'AccWidget.py', 
        'Auth.py',
        'AutomationWidget.py',
        'ConfigManager.py',
        'Crosshair.py',
        'DefaultSettings.py',
        'GameChecker.py',
        'GamesWidget.py',
        'keyauth.py',
        'MiscWidget.py',
        'rapidfire.py',
        'RecoilWidget.py',
        'StatsWidget.py',
        'updater.py',
        'visuals.py',
        'WindowManager.py'
    ]

    # Liste der Module, die eingebunden werden sollen
    hidden_imports = [
        'flet',
        'pystray', 
        'psutil',
        'win32gui',
        'win32api', 
        'win32con',
        'keyboard',
        'keyauth', 
        'hashlib',  
        'uuid',     
        'ctypes'   
        'keyauth',
        'hashlib',
        'uuid',
        'ctypes',
        'json',
        'datetime',
        'socket',
        'urllib.request',
        're'
    ]

    # Erstelle die PyInstaller Kommandozeile
    pyinstaller_command = [
        'pyinstaller',
        '--noconfirm',
        '--onefile',
        '--windowed',
        '--icon=Vanguard.ico',
        '--name=Vanguard',
        '--distpath=build',
        '--clean',
        '--manifest=manifest.xml',
    ]

    # Füge hidden imports hinzu
    for import_name in hidden_imports:
        pyinstaller_command.extend(['--hidden-import', import_name])

    # Füge alle Source Files als zusätzliche Daten hinzu
    for source_file in source_files:
        source_path = os.path.abspath(source_file)
        pyinstaller_command.extend(['--add-data', f'{source_path};.'])

    # Füge Versionsinformationen hinzu
    pyinstaller_command.extend([
        '--version-file=VERSION.txt',
        'main.py',
    ])

    print("Starte PyInstaller Build...")
    try:
        result = subprocess.run(pyinstaller_command, check=True)
        print("Build abgeschlossen!")
    except subprocess.CalledProcessError as e:
        print(f"Fehler beim Build: {e}")
    except Exception as e:
        print(f"Unerwarteter Fehler: {e}")