
import flet as ft
import json
import platform
import uuid
import ctypes
import sys
import re
import os
import time
import hashlib
import asyncio
from keyauth import api, KeyAuthException
from DefaultSettings import *
from pathlib import Path

DEBUG = True

def debug_print(*args, **kwargs):
    if DEBUG:
        print(*args, **kwargs)

def get_translation(key, current_language="en"):
    try:
        base_dir = Path(os.path.expanduser("~")) / MAIN_FOLDER
        lang_path = base_dir / "assets" / "languages" / "LANGUAGES.json"
        
        if not os.path.exists(lang_path):
            return get_default_text(key)
            
        with open(lang_path, 'r', encoding='utf-8') as f:
            languages = json.load(f)
            
        if current_language in languages and "account" in languages[current_language]:
            if key in languages[current_language]["account"]:
                return languages[current_language]["account"][key]
    except Exception as e:
        print(f"Translation error: {e}")
    return get_default_text(key)

def get_default_text(key):
    defaults = {
        "account_subscription": "Account & Subscription",
        "username": "Username",
        "password": "Password",
        "key": "Key",
        "key_status": "Key Status",
        "active": "Active",
        "expired": "Expired",
        "created": "Created",
        "expires": "Expires",
        "hwid": "HWID",
        "system_info": "System Information",
        "cpu": "CPU",
        "gpu": "GPU",
        "os": "OS",
        "motherboard": "Motherboard",
        "bios": "BIOS",
        "ip": "IP"
    }
    return defaults.get(key, "")


def create_sensitive_text(text: str, max_length: int = None, copyable: bool = True):
    def truncate_text(text: str) -> str:
        if max_length and len(text) > max_length:
            return text[:max_length] + "..."
        return text

    async def copy_to_clipboard(e):
        copy_button.icon = ft.icons.CHECK
        copy_button.icon_color = ft.colors.GREEN
        e.page.set_clipboard(text)
        await asyncio.sleep(0.5)
        copy_button.icon = ft.icons.COPY
        copy_button.icon_color = TEXT_COLOR
        copy_button.update()

    def toggle_visibility(e):
        text_widget.value = truncate_text(text) if text_widget.value.startswith("•") else "•" * min(len(text), 13)
        text_widget.update()

    text_widget = ft.Text(
        "•" * min(len(text), 13),
        color=ACCENT_COLOR,
        size=13,
    )
    
    row_controls = [
        ft.GestureDetector(
            content=text_widget,
            on_tap=toggle_visibility,
            mouse_cursor=ft.MouseCursor.CLICK
        )
    ]

    if copyable:
        copy_button = ft.IconButton(
            icon=ft.icons.COPY,
            icon_color=TEXT_COLOR,
            icon_size=14,
            on_click=copy_to_clipboard,
            tooltip="Copy to clipboard",
            style=ft.ButtonStyle(padding=5),
        )
        row_controls.append(copy_button)
    
    return ft.Container(
        content=ft.Row(
            controls=row_controls,
            spacing=0,
            alignment=ft.MainAxisAlignment.START,
            vertical_alignment=ft.CrossAxisAlignment.CENTER,
        ),
        padding=0,
        height=25,
    )

def getchecksum():
    md5_hash = hashlib.md5()
    try:
        # Für die kompilierte Version
        if getattr(sys, 'frozen', False):
            file_path = sys.executable
        # Für die IDE Version
        else:
            file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
            
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
        return md5_hash.hexdigest()
    except Exception as e:
        print(f"Checksum error: {e}")
        return ""
   
def load_auth_config():
    config_path = os.path.join(os.path.expanduser("~"), MAIN_FOLDER, "auth", "licence.json")
    try:
        with open(config_path, "r") as f:
            return json.load(f)
    except:
        return None

STATUS_CONTAINER_PROPS = {
    "width": 840,
    "height": 230,
    "bgcolor": WIDGET_BG, 
    "border_radius": 4,
    "padding": 15,
    "margin": ft.margin.only(left=-10, top=-38, right=-10, bottom=-10),
    "border": ft.border.all(1, ft.Colors.with_opacity(1, BORDER_COLOR)),
}

DETAILS_CONTAINER_PROPS = {
    "width": 840,
    "height": 270,
    "bgcolor": WIDGET_BG, 
    "border_radius": 4,
    "padding": 20,
    "margin": ft.margin.only(left=-10, top=-5, right=-10, bottom=-40),
    "border": ft.border.all(1, ft.Colors.with_opacity(1, BORDER_COLOR)),
}

def convert_timestamp(timestamp):
    try:
        from datetime import datetime
        dt = datetime.fromtimestamp(int(timestamp))
        return dt.strftime("%d/%m/%Y %H:%M:%S")
    except:
        return timestamp

def get_days_passed(created_timestamp):
    try:
        from datetime import datetime
        created_date = datetime.fromtimestamp(int(created_timestamp))
        current_date = datetime.now()
        days_passed = (current_date - created_date).days
        return days_passed
    except:
        return 0

def get_days_remaining(expiry_timestamp):
    try:
        from datetime import datetime
        expiry_date = datetime.fromtimestamp(int(expiry_timestamp))
        current_date = datetime.now()
        days_remaining = (expiry_date - current_date).days
        return max(0, days_remaining)  # Don't return negative days
    except:
        return 0
    
def create_account_status_widget(current_language="en"):
    auth_config = load_auth_config()
    if not auth_config or not auth_config.get("remember", False):
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("Not logged in", color=TEXT_COLOR)
                ],
                spacing=8,
            ),
            **STATUS_CONTAINER_PROPS
        )

    try:
        checksum = getchecksum()
        
        keyauthapp = api(
            name = "VANGUARD", 
            ownerid = "xsGpVEL38v", 
            version = "1.0", 
            hash_to_check = checksum
        )

        keyauthapp.login(
            auth_config["username"],
            auth_config["password"]
        )

        controls = [
            ft.Container(
                content=ft.Column([
                    ft.Row(
                        controls=[
                            ft.Icon(ft.icons.PERSON, color=ACCENT_COLOR, size=20),
                            ft.Text(
                                get_translation("account_subscription", current_language),
                                color=ACCENT_COLOR,
                                size=16,
                                weight=ft.FontWeight.BOLD,
                            ),
                        ],
                        spacing=10,
                    ),
                    ft.Container(
                        content=ft.Divider(color=ft.Colors.with_opacity(0.1, ACCENT_COLOR), height=1),
                        margin=ft.margin.only(top=0, bottom=0),
                    ),
                ]),
                margin=ft.margin.only(bottom=5),
            ),
            ft.Row(
                controls=[
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Icon(ft.icons.PERSON_OUTLINE, color=TEXT_COLOR, size=16),
                                ft.Text(f"{get_translation('username', current_language)}:", color=TEXT_COLOR),
                                ft.Text(auth_config["username"], color=ACCENT_COLOR),
                            ],
                            spacing=10,
                        ),
                        expand=True,
                    ),
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Icon(ft.icons.LOCK_OUTLINE, color=TEXT_COLOR, size=16),
                                ft.Text(f"{get_translation('password', current_language)}:", color=TEXT_COLOR),
                                create_sensitive_text(auth_config["password"], max_length=20),
                            ],
                            spacing=10,
                        ),
                        expand=True,
                    ),
                ],
                spacing=30,
            ),
            ft.Row(
                controls=[
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Icon(ft.icons.KEY, color=TEXT_COLOR, size=16),
                                ft.Text(f"{get_translation('key', current_language)}:", color=TEXT_COLOR),
                                create_sensitive_text(auth_config.get("license_key", "N/A"), max_length=20),
                            ],
                            spacing=10,
                        ),
                        expand=True,
                    ),
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Icon(ft.icons.ACCESS_TIME, color=TEXT_COLOR, size=16),
                                ft.Text(f"{get_translation('key_status', current_language)}:", color=TEXT_COLOR),
                                ft.Text(
                                    get_translation("active", current_language) if keyauthapp and int(keyauthapp.user_data.expires) > int(time.time()) 
                                    else get_translation("expired", current_language),
                                    color=ACCENT_COLOR if keyauthapp and int(keyauthapp.user_data.expires) > int(time.time()) else ft.colors.RED
                                ),
                            ],
                            spacing=10,
                        ),
                        expand=True,
                    ),
                ],
                spacing=30,
            ),
        ]

        # Füge KeyAuth-spezifische Informationen hinzu, wenn verfügbar
        if keyauthapp:
            controls.extend([
                ft.Row(
                    controls=[
                        ft.Container(
                            content=ft.Row(
                                controls=[
                                    ft.Icon(ft.icons.CALENDAR_TODAY, color=TEXT_COLOR, size=16),
                                    ft.Text(f"{get_translation('created', current_language)}:", color=TEXT_COLOR),
                                    ft.Text(
                                        f"{convert_timestamp(keyauthapp.user_data.createdate)}", 
                                        color=ACCENT_COLOR
                                    ),
                                ],
                                spacing=10,
                            ),
                            expand=True,
                        ),
                        ft.Container(
                            content=ft.Row(
                                controls=[
                                    ft.Icon(ft.icons.TIMER, color=TEXT_COLOR, size=16),
                                    ft.Text(f"{get_translation('expires', current_language)}:", color=TEXT_COLOR),
                                    ft.Text(
                                        f"{convert_timestamp(keyauthapp.user_data.expires)}", 
                                        color=ACCENT_COLOR
                                    ),
                                ],
                                spacing=10,
                            ),
                            expand=True,
                        ),
                    ],
                    spacing=30,
                ),
                ft.Row(
                    controls=[
                        ft.Container(
                            content=ft.Row(
                                controls=[
                                    ft.Icon(ft.icons.FINGERPRINT, color=TEXT_COLOR, size=16),
                                    ft.Text(f"{get_translation('hwid', current_language)}:", color=TEXT_COLOR),
                                    create_sensitive_text(keyauthapp.user_data.hwid, max_length=None, copyable=False),
                                ],
                                spacing=10,
                            ),
                            expand=True,
                        ),
                    ],
                    spacing=30,
                ),
            ])

        layout = ft.Container(
            content=ft.Column(
                controls=controls,
                spacing=15,
            ),
            **STATUS_CONTAINER_PROPS
        )

        def update_language(language_key):
            nonlocal current_language
            current_language = language_key
            try:
                new_content = create_account_status_widget(language_key).content
                if layout.page:
                    layout.content = new_content
                    layout.update()
            except Exception as e:
                print(f"Error updating account language: {e}")
                import traceback
                traceback.print_exc()

        setattr(layout, 'update_language', update_language)
        return layout

    except Exception as e:
        print(f"Error in create_account_status_widget: {e}")
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(f"Error: {str(e)}", color=TEXT_COLOR)
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            **STATUS_CONTAINER_PROPS
        )

def create_account_details_widget(current_language="en"):
   try:
       def get_cpu_info():
           try:
               import subprocess
               import re
               
               cmd = "wmic cpu get name, numberofcores, numberoflogicalprocessors, maxclockspeed /format:value"
               output = subprocess.check_output(cmd, shell=True).decode()
               
               name = re.search(r"Name=(.+)", output)
               cores = re.search(r"NumberOfCores=(\d+)", output)
               threads = re.search(r"NumberOfLogicalProcessors=(\d+)", output)
               clock = re.search(r"MaxClockSpeed=(\d+)", output)
               
               if all([name, cores, threads, clock]):
                   clock_ghz = round(float(clock.group(1)) / 1000, 2)
                   return f"{name.group(1).strip()}"
               return "CPU information unavailable"
           except:
               return "CPU information unavailable"

       def get_gpu_info():
           try:
               import subprocess
               cmd = """powershell "Get-WmiObject Win32_VideoController | ForEach-Object { $_.Description + '|' + $_.AdapterRAM + '|' + $_.DriverVersion }" """
               output = subprocess.check_output(cmd, shell=True).decode('utf-8', errors='ignore')
               
               gpus = []
               for line in output.splitlines():
                   if line.strip():
                       try:
                           description, ram, driver = line.split('|')
                           if not any(x in description.lower() for x in [
                               "radeon(tm) graphics", 
                               "graphics processor", 
                               "igpu",
                               "amd radeon(tm) graphics"
                           ]):
                               try:
                                   vram = round(int(ram) / (1024**3), 2)
                                   gpu_str = f"{description.strip()}"
                               except:
                                   gpu_str = f"{description.strip()}"
                               gpus.append(gpu_str)
                       except:
                           if not any(x in line.lower() for x in [
                               "radeon(tm) graphics", 
                               "graphics processor", 
                               "igpu",
                               "amd radeon(tm) graphics"
                           ]):
                               gpus.append(line.strip())

               return " & ".join(gpus) if gpus else "No dedicated GPU found"
           except Exception as e:
               print(f"GPU Error: {str(e)}")
               cmd = """powershell "Get-WmiObject Win32_VideoController | Select-Object Description" """
               try:
                   output = subprocess.check_output(cmd, shell=True).decode('utf-8', errors='ignore')
                   for line in output.splitlines():
                       if "Description" not in line and line.strip():
                           if not any(x in line.lower() for x in [
                               "radeon(tm) graphics", 
                               "graphics processor", 
                               "igpu",
                               "amd radeon(tm) graphics"
                           ]):
                               return line.strip()
               except:
                   return "GPU information unavailable"

       def get_windows_version():
           try:
               import subprocess
               cmd = 'wmic os get caption, buildnumber, osarchitecture /format:value'
               output = subprocess.check_output(cmd, shell=True).decode()
               
               caption = re.search(r"Caption=(.+)", output)
               build = re.search(r"BuildNumber=(.+)", output)
               arch = re.search(r"OSArchitecture=(.+)", output)
               
               if all([caption, build, arch]):
                   return f"{caption.group(1).strip()} Build {build.group(1).strip()} ({arch.group(1).strip()})"
               return "OS information unavailable"
           except:
               return "OS information unavailable"

       def get_ip_addresses():
           try:
               import socket
               import urllib.request
               
               def get_local_ip():
                   try:
                       s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                       s.connect(("*******", 80))
                       local_ip = s.getsockname()[0]
                       s.close()
                       return local_ip
                   except:
                       return "Not available"
               
               def get_external_ip():
                   try:
                       external_ip = urllib.request.urlopen('https://api.ipify.org').read().decode('utf8')
                       return external_ip
                   except:
                       return "Not available"
                       
               local_ip = get_local_ip()
               external_ip = get_external_ip()
               
               return f"Local: {local_ip} | External: {external_ip}"
           except:
               return "IP information unavailable"

       def get_motherboard_info():
           try:
               import subprocess
               cmd = 'wmic baseboard get product,manufacturer /format:value'
               output = subprocess.check_output(cmd, shell=True).decode()
               
               manufacturer = re.search(r"Manufacturer=(.+)", output)
               product = re.search(r"Product=(.+)", output)
               
               if manufacturer and product:
                   return f"{manufacturer.group(1).strip()} {product.group(1).strip()}"
               return "Motherboard information unavailable"
           except:
               return "Motherboard information unavailable"

       def get_bios_info():
           try:
               import subprocess
               cmd = 'wmic bios get version,manufacturer /format:value'
               output = subprocess.check_output(cmd, shell=True).decode()
               
               version = re.search(r"Version=(.+)", output)
               manufacturer = re.search(r"Manufacturer=(.+)", output)
               
               if version and manufacturer:
                   return f"{manufacturer.group(1).strip()} {version.group(1).strip()}"
               return "BIOS information unavailable"
           except:
               return "BIOS information unavailable"
           
       layout = ft.Container(
           content=ft.Column(
               controls=[
                   ft.Container(
                       content=ft.Column([
                           ft.Row(
                               controls=[
                                   ft.Icon(ft.icons.COMPUTER, color=ACCENT_COLOR, size=20),
                                   ft.Text(
                                       get_translation("system_info", current_language),
                                       color=ACCENT_COLOR,
                                       size=16,
                                       weight=ft.FontWeight.BOLD,
                                   ),
                               ],
                               spacing=10,
                           ),
                           ft.Container(
                               content=ft.Divider(color=ft.Colors.with_opacity(0.1, ACCENT_COLOR), height=1),
                               margin=ft.margin.only(top=0, bottom=0),
                           ),
                       ]),
                       margin=ft.margin.only(bottom=5),
                   ),
                   ft.Row(
                       controls=[
                           ft.Icon(ft.icons.MEMORY, color=TEXT_COLOR, size=16),
                           ft.Text(f"{get_translation('cpu', current_language)}:", color=TEXT_COLOR),
                           ft.Text(get_cpu_info(), color=ACCENT_COLOR),
                       ],
                       spacing=10,
                   ),
                   ft.Row(
                       controls=[
                           ft.Icon(ft.icons.VIDEOGAME_ASSET, color=TEXT_COLOR, size=16),
                           ft.Text(f"{get_translation('gpu', current_language)}:", color=TEXT_COLOR),
                           ft.Text(get_gpu_info(), color=ACCENT_COLOR),
                       ],
                       spacing=10,
                   ),
                   ft.Row(
                       controls=[
                           ft.Icon(ft.icons.DESKTOP_WINDOWS, color=TEXT_COLOR, size=16),
                           ft.Text(f"{get_translation('os', current_language)}:", color=TEXT_COLOR),
                           ft.Text(get_windows_version(), color=ACCENT_COLOR),
                       ],
                       spacing=10,
                   ),
                   ft.Row(
                       controls=[
                           ft.Icon(ft.icons.DASHBOARD, color=TEXT_COLOR, size=16),
                           ft.Text(f"{get_translation('motherboard', current_language)}:", color=TEXT_COLOR),
                           ft.Text(get_motherboard_info(), color=ACCENT_COLOR),
                       ],
                       spacing=10,
                   ),
                   ft.Row(
                       controls=[
                           ft.Icon(ft.icons.MEMORY, color=TEXT_COLOR, size=16),
                           ft.Text(f"{get_translation('bios', current_language)}:", color=TEXT_COLOR),
                           ft.Text(get_bios_info(), color=ACCENT_COLOR),
                       ],
                       spacing=10,
                   ),
                   ft.Row(
                       controls=[
                           ft.Icon(ft.icons.PUBLIC, color=TEXT_COLOR, size=16),
                           ft.Text(f"{get_translation('ip', current_language)}:", color=TEXT_COLOR),
                           create_sensitive_text(get_ip_addresses(), max_length=None, copyable=False),
                       ],
                       spacing=10,
                   ),
               ],
               spacing=10,
           ),
           **DETAILS_CONTAINER_PROPS
       )

       def update_language(language_key):
           nonlocal current_language
           current_language = language_key
           try:
               new_content = create_account_details_widget(language_key).content
               if layout.page:
                   layout.content = new_content
                   layout.update()
           except Exception as e:
               print(f"Error updating system info language: {e}")
               import traceback
               traceback.print_exc()

       setattr(layout, 'update_language', update_language)
       return layout

   except Exception as e:
       return ft.Container(
           content=ft.Column(
               controls=[
                   ft.Text(f"Error loading system information: {str(e)}", 
                          color=TEXT_COLOR)
               ],
               horizontal_alignment=ft.CrossAxisAlignment.CENTER,
           ),
           **DETAILS_CONTAINER_PROPS
       )