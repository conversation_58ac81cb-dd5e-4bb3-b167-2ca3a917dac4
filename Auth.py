import flet as ft
from keyauth import api, KeyAuthException
import os
import hashlib
import json
import sys
import time
import asyncio
import threading
from DefaultSettings import *
from updater import UpdateManager, FileDownloader

CONFIG_FILE = os.path.join(os.path.expanduser("~"), MAIN_FOLDER, "auth", "licence.json")

def save_credentials(username, password, license_key=None, remember=False):
    os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
    
    try:
        with open(CONFIG_FILE, "r") as f:
            existing_data = json.load(f)
    except:
        existing_data = {}
    
    data = {
        "username": username,
        "password": password,
        "remember": remember
    }
    
    if "license_key" in existing_data:
        data["license_key"] = existing_data["license_key"]
    elif license_key:
        data["license_key"] = license_key

    with open(CONFIG_FILE, "w") as f:
        json.dump(data, f)

def load_credentials():
    try:
        with open(CONFIG_FILE, "r") as f:
            return json.load(f)
    except:
        return {"remember": False}

def getchecksum():
    md5_hash = hashlib.md5()
    
    if getattr(sys, 'frozen', False):
        exe_path = sys.executable
        with open(exe_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
    else:
        file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
                
    return md5_hash.hexdigest()

def main(page: ft.Page):
    page.title = NAME
    page.bgcolor = MAIN_BG
    page.padding = 0
    page.window.frameless = True
    page.window.resizable = False
    page.window.minimizable = False
    page.window.maximizable = False
    
    def run_update():
        page.window.width = 350
        page.window.height = 400
        page.update()
        
        downloader = FileDownloader(page)
        try:
            downloader.update_status("Checking assets...", 0.1)
            manager = UpdateManager()

            downloader.update_status("Updating files...", 0.3)
            manager.update_files()
            
            downloader.update_status("Starting...", 1.0)
            time.sleep(1)
            
            page.clean()
            page.window.width = 350
            page.window.height = 400
            page.update()
            
            setup_auth_ui()
            
        except Exception as e:
            downloader.update_status(f"Error occurred: {str(e)}", 1.0)
            time.sleep(2)
            page.window.close()

    def setup_auth_ui():
        saved_creds = load_credentials()

        login_view = ft.Container(visible=True)
        register_view = ft.Container(visible=False)

        def create_textfield(label: str, password: bool = False, icon: ft.icons = None):
            return ft.TextField(
                label=label,
                width=240,
                height=45,
                border_radius=4,
                bgcolor=WIDGET_BG,
                color=TEXT_COLOR,
                label_style=ft.TextStyle(color=TEXT_COLOR, size=TEXT_SIZE),
                password=password,
                can_reveal_password=password,
                prefix_icon=icon,
                border_width=1,
                focused_border_color=BORDER_COLOR,
                cursor_color=ACCENT_COLOR,
                text_size=TEXT_SIZE
            )

        def create_button(text: str, on_click):
            return ft.ElevatedButton(
                text=text,
                width=240,
                height=40,
                on_click=on_click,
                style=ft.ButtonStyle(
                    bgcolor={ft.ControlState.DEFAULT: ft.Colors.with_opacity(0.1, ACCENT_COLOR)},
                    shape=ft.RoundedRectangleBorder(radius=4),
                    color={ft.ControlState.DEFAULT: TEXT_COLOR}
                )
            )

        def handle_error(message):
            error_text.value = message
            error_text.visible = True
            page.update()

        def switch_view(to_login: bool):
            login_view.visible = to_login
            register_view.visible = not to_login
            error_text.visible = False
            page.update()

        def load_saved_credentials():
            if saved_creds.get("remember", False):
                login_username.value = saved_creds.get("username", "")
                login_password.value = saved_creds.get("password", "")
                page.update()

        def on_login(e):
            try:
                username = login_username.value
                password = login_password.value
                remember = remember_me_checkbox.value

                if not all([username, password]):
                    handle_error("Please fill in all fields")
                    return
                    
                if len(username) > 50 or len(password) > 50:
                    handle_error("Username and password must not exceed 50 characters")
                    return

                keyauthapp = api(
                    name = "VANGUARD", 
                    ownerid = "xsGpVEL38v", 
                    version = "1.0",
                    hash_to_check = getchecksum()
                )

                if keyauthapp.login(username, password):
                    save_credentials(username, password, remember=remember)
                    #handle_error(" > HS_DONE:VALID_SESSION")
                    time.sleep(1)
                    
                    from main import AuthState
                    AuthState.authenticated = True
                    
                    page.clean()
                    from main import main
                    main(page)

            except KeyAuthException as e:
                handle_error(str(e))
            except Exception as e:
                handle_error(str(e))
                print(f"Error: {str(e)}")

        def on_register(e):
            try:
                username = register_username.value
                password = register_password.value
                license_key = register_key.value

                if not all([username, password, license_key]):
                    handle_error("Please fill in all fields")
                    return
                    
                if len(username) > 50 or len(password) > 50 or len(license_key) > 50:
                    handle_error("All fields must not exceed 50 characters")
                    return

                keyauthapp = api(
                    name = "VANGUARD", 
                    ownerid = "xsGpVEL38v", 
                    version = "1.0",
                    hash_to_check = getchecksum()
                )

                keyauthapp.register(username, password, license_key)
                save_credentials(
                    username=username,
                    password=password, 
                    license_key=license_key,
                    remember=False
                )
                
                switch_view(True)
                handle_error("Registration successful! Please login.")

            except KeyAuthException as e:
                handle_error(str(e))
            except Exception as e:
                handle_error(str(e))

        custom_title_bar = ft.Container(
            height=30,
            bgcolor=WIDGET_BG,
            content=ft.Row(
                controls=[
                    ft.WindowDragArea(
                        content=ft.Row(
                            controls=[
                                ft.Container(
                                    content=ft.Text(
                                        NAME.upper(),
                                        color=ACCENT_COLOR,
                                        size=TITLE_SIZE,
                                        weight=ft.FontWeight.BOLD
                                    ),
                                    padding=ft.padding.only(left=10),
                                ),
                            ],
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                        expand=True
                    ),
                    ft.Container(
                        content=ft.IconButton(
                            icon=ft.Icons.CLOSE,
                            icon_color=TEXT_COLOR,
                            icon_size=TITLE_SIZE,
                            on_click=lambda _: page.window.close(),
                            style=ft.ButtonStyle(
                                overlay_color=ft.Colors.TRANSPARENT,
                                color={ft.ControlState.DEFAULT: TEXT_COLOR},
                            )
                        ),
                        padding=0,
                        alignment=ft.alignment.center,
                        width=45,
                        height=30,
                    ),
                ],
                spacing=0,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
            ),
        )

        login_username = create_textfield("Username", icon=ft.Icons.PERSON)
        login_username.max_length = 50

        login_password = create_textfield("Password", password=True, icon=ft.Icons.LOCK)
        login_password.max_length = 50
        remember_me_checkbox = ft.Checkbox(
            scale=0.9,
            value=saved_creds.get("remember", False),
            fill_color={
                ft.ControlState.DEFAULT: "transparent",
                ft.ControlState.SELECTED: ACCENT_COLOR,
            },
            check_color=TEXT_COLOR,
        )

        remember_me = ft.Container(
            content=ft.Row(
                controls=[
                    remember_me_checkbox,
                    ft.Text(
                        "Remember me",
                        color=TEXT_COLOR,
                        size=TEXT_SIZE,
                        weight=ft.FontWeight.W_500,
                    )
                ],
                spacing=5,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            margin=ft.margin.only(left=30),
            ink=False,
        )
        login_button = create_button("LOGIN", on_login)

        register_username = create_textfield("Username", icon=ft.Icons.PERSON)
        register_username.max_length = 50

        register_password = create_textfield("Password", password=True, icon=ft.Icons.LOCK)
        register_password.max_length = 50

        register_key = create_textfield("License Key", icon=ft.Icons.KEY)
        register_key.max_length = 50
        register_button = create_button("REGISTER", on_register)

        error_text = ft.Text(
            color=ACCENT_COLOR,
            size=TEXT_SIZE,
            visible=False,
            text_align=ft.TextAlign.CENTER,
            weight=ft.FontWeight.W_500
        )

        error_container = ft.Container(
            content=error_text,
            margin=ft.margin.only(top=10, bottom=10)
        )
            
        register_switch = ft.Container(
            content=ft.Text(
                "Need an account? Register",
                color=TEXT_COLOR,
                size=TEXT_SIZE,
                weight=ft.FontWeight.W_500,
                text_align=ft.TextAlign.CENTER,
            ),
            on_click=lambda _: switch_view(False),
            margin=ft.margin.only(top=15),
            ink=False,
        )

        login_switch = ft.Container(
            content=ft.Text(
                "Already have an account? Login",
                color=TEXT_COLOR,
                size=TEXT_SIZE,
                weight=ft.FontWeight.W_500,
                text_align=ft.TextAlign.CENTER,
            ),
            on_click=lambda _: switch_view(True),
            margin=ft.margin.only(top=15),
            ink=False,
        )

        login_view.content = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("LOGIN", size=TITLE_SIZE, weight=ft.FontWeight.BOLD, color=ACCENT_COLOR),
                    ft.Container(height=15),
                    login_username,
                    ft.Container(height=8),
                    login_password,
                    ft.Container(
                        content=remember_me,
                        margin=ft.margin.only(top=5, bottom=10),
                    ),
                    login_button,
                    register_switch,
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=0,
            ),
            border_radius=4,
            padding=10,
        )

        register_view.content = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("REGISTER", size=TITLE_SIZE, weight=ft.FontWeight.BOLD, color=ACCENT_COLOR),
                    ft.Container(height=15),
                    register_username,
                    ft.Container(height=8),
                    register_password,
                    ft.Container(height=8),
                    register_key,
                    ft.Container(height=15),
                    register_button,
                    login_switch,
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=0,
            ),
            border_radius=4,
            padding=10,
        )

        page.add(
            ft.Column(
                controls=[
                    custom_title_bar,
                    ft.Divider(
                        height=1,
                        color=BORDER_COLOR
                    ),
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                login_view,
                                register_view,
                                error_container,
                            ],
                            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                        padding=ft.padding.all(5),
                    )
                ],
                expand=True,
                spacing=0,
            )
        )

        load_saved_credentials()

    threading.Thread(target=run_update, daemon=True).start()

if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.FLET_APP, assets_dir="assets")