import keyboard
import threading
from pynput.mouse import <PERSON><PERSON>, Controller as <PERSON>Controller
import time
from typing import Callable

class AutoBunnyHop:
    def __init__(self):
        self.running = False
        self.thread = None
        self.enabled = False
        self.hook_active = False

    def bunny_hop(self):
        while self.running and self.enabled:
            keyboard.press('space')
            time.sleep(0.01)
            keyboard.release('space')
            time.sleep(0.01)

    def on_space_press(self):
        if self.enabled and not self.running:
            self.running = True
            self.thread = threading.Thread(target=self.bunny_hop)
            self.thread.start()

    def on_space_release(self):
        if self.running:
            self.running = False
            if self.thread:
                self.thread.join()

    def toggle(self, enabled: bool):
        self.enabled = enabled
        if enabled and not self.hook_active:
            keyboard.on_press_key('space', lambda _: self.on_space_press())
            keyboard.on_release_key('space', lambda _: self.on_space_release())
            self.hook_active = True
        elif not enabled and self.hook_active:
            keyboard.unhook_key('space')
            self.hook_active = False
            self.running = False
            if self.thread:
                self.thread.join()

class AutoPing:
    def __init__(self):
        self.enabled = False
        self.mouse_controller = MouseController()
        self.last_click_pos = (0, 0)
        self.last_click_time = 0

    def on_click(self, x: int, y: int, button: Button, pressed: bool):
        if self.enabled and button == Button.left:
            if pressed:
                self.last_click_pos = (x, y)
                self.last_click_time = time.time()
                threading.Thread(target=self.check_press_duration).start()

    def check_press_duration(self):
        start_time = time.time()
        while self.enabled and time.time() - start_time < 0.3:
            time.sleep(0.01)
        if self.enabled:
            current_pos = self.mouse_controller.position
            if (abs(current_pos[0] - self.last_click_pos[0]) < 5 and 
                abs(current_pos[1] - self.last_click_pos[1]) < 5):
                self.mouse_controller.click(Button.middle)

    def toggle(self, enabled: bool):
        self.enabled = enabled

class AutoSprint:
    def __init__(self):
        self.enabled = False
        self.sprint_key = "shift"
        self.thread = None

    def sprint_loop(self):
        while self.enabled:
            if keyboard.is_pressed('w'):
                keyboard.press(self.sprint_key)
            else:
                keyboard.release(self.sprint_key)
            time.sleep(0.01)

    def toggle(self, enabled: bool):
        self.enabled = enabled
        if enabled and not self.thread:
            self.thread = threading.Thread(target=self.sprint_loop, daemon=True)
            self.thread.start()
        elif not enabled:
            self.thread = None

    def set_sprint_key(self, key: str):
        self.sprint_key = key.lower()
