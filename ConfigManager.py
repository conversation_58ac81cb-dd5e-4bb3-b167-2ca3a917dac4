import json
import os
from pathlib import Path
from DefaultSettings import *

class ConfigManager:
    def __init__(self):
        self.base_dir = Path(os.path.expanduser("~")) / MAIN_FOLDER
        self.config_dir = self.base_dir / "configs"
        self.current_config_file = self.base_dir / "current_config.txt"
        
        # Erstelle die benötigten Verzeichnisse
        self.base_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)

    def _convert_to_int(self, data):
        """Convert float values to integers in the config data"""
        if isinstance(data, dict):
            return {k: self._convert_to_int(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._convert_to_int(item) for item in data]
        elif isinstance(data, float):
            return int(data)
        return data

    def save_config(self, config_name, settings):
        try:
            if not config_name.endswith('.json'):
                config_name += '.json'
            
            config_path = self.config_dir / config_name
            
            settings = self._convert_to_int(settings)
            
            with open(config_path, 'w') as f:
                json.dump(settings, f, indent=4)
            
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False

    def load_config(self, config_name):
        try:
            if not config_name.endswith('.json'):
                config_name += '.json'
            
            config_path = self.config_dir / config_name
            
            if not config_path.exists():
                return None
            
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
            return None

    def get_config_files(self):
        try:
            return [f.name for f in self.config_dir.glob('*.json')]
        except Exception as e:
            print(f"Error getting config files: {e}")
            return []

    def save_current_config(self, config_name):
        try:
            with open(self.current_config_file, 'w') as f:
                f.write(config_name)
            return True
        except Exception as e:
            print(f"Error saving current config: {e}")
            return False

    def get_current_config(self):
        try:
            if not self.current_config_file.exists():
                return None
            
            with open(self.current_config_file, 'r') as f:
                return f.read().strip()
        except Exception as e:
            print(f"Error getting current config: {e}")
            return None

    def delete_config(self, config_name):
        try:
            if not config_name.endswith('.json'):
                config_name += '.json'
            
            config_path = self.config_dir / config_name
            
            if config_path.exists():
                config_path.unlink()
                return True
            return False
        except Exception as e:
            print(f"Error deleting config: {e}")
            return False