import win32gui
import win32con
import win32api
import math
import threading
import ctypes
import time
from GameChecker import can_perform_action
from ctypes import windll

class Crosshair:
    def __init__(self):
        self.color = (255, 0, 0)
        self.thickness = 3  
        self.size = 15    
        self.enabled = False 
        self.hwnd = None
        self.type = "cross"
        self.class_name = f"CrosshairOverlay_{id(self)}"
        self._create_window()
        if self.hwnd:
            self.make_click_through()
        
        self.running = True
        self.check_thread = threading.Thread(target=self._check_game_status)
        self.check_thread.daemon = True
        self.check_thread.start()


    def _check_game_status(self):
        while self.running:
            can_show = can_perform_action()
            if can_show and self.enabled:
                if self.hwnd:
                    win32gui.ShowWindow(self.hwnd, win32con.SW_SHOW)
            else:
                if self.hwnd:
                    win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)
            time.sleep(1)
            
    def __del__(self):
        self.running = False 
        if self.check_thread.is_alive():
            self.check_thread.join(timeout=1)
        if self.hwnd and win32gui.IsWindow(self.hwnd):
            try:
                win32gui.DestroyWindow(self.hwnd)
                win32gui.UnregisterClass(self.class_name, None)
            except:
                pass
            
    def _create_window(self):
        if self.hwnd:
            try:
                win32gui.DestroyWindow(self.hwnd)
                self.hwnd = None
            except:
                pass
        try:
            win32gui.UnregisterClass(self.class_name, None)
        except:
            pass

        wc = win32gui.WNDCLASS()
        wc.lpszClassName = self.class_name
        wc.hCursor = win32gui.LoadCursor(0, win32con.IDC_ARROW)
        wc.hbrBackground = win32gui.GetStockObject(win32con.NULL_BRUSH)
        wc.lpfnWndProc = self.wndProc
        wc.style = win32con.CS_HREDRAW | win32con.CS_VREDRAW

        try:
            self.atom = win32gui.RegisterClass(wc)
        except Exception as e:
            print(f"Failed to register window class: {e}")
            return False
        screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
        screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)

        try:
            self.hwnd = win32gui.CreateWindowEx(
                win32con.WS_EX_LAYERED | win32con.WS_EX_TRANSPARENT | win32con.WS_EX_TOPMOST | win32con.WS_EX_TOOLWINDOW,
                self.class_name,
                "Crosshair",
                win32con.WS_POPUP | win32con.WS_VISIBLE,
                0, 0, screen_width, screen_height,
                0, 0, 0, None
            )
            win32gui.SetLayeredWindowAttributes(self.hwnd, 0, 255, win32con.LWA_COLORKEY)
            style = win32gui.GetWindowLong(self.hwnd, win32con.GWL_EXSTYLE)
            win32gui.SetWindowLong(self.hwnd, win32con.GWL_EXSTYLE, 
                                 style | win32con.WS_EX_TRANSPARENT | win32con.WS_EX_LAYERED)
            win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)
            try:
                windll.user32.SetWindowDisplayAffinity(self.hwnd, 0x00000011)
            except Exception as e:
                print(f"Failed to set window display affinity: {e}")
            
            return True
        except Exception as e:
            print(f"Failed to create window: {e}")
            return False

    def make_click_through(self):
        if not self.hwnd:
            return
        try:
            win32gui.SetWindowLong(
                self.hwnd,
                win32con.GWL_EXSTYLE,
                win32gui.GetWindowLong(self.hwnd, win32con.GWL_EXSTYLE) | win32con.WS_EX_TRANSPARENT
            )
        except Exception as e:
            print(f"Failed to make window click-through: {e}")

    def update_settings(self, enabled=None, size=None, thickness=None, type=None, color=None, stream_proof=None):
        if enabled is not None:
            self.enabled = enabled
            if self.hwnd:
                try:
                    if self.enabled:
                        if not win32gui.IsWindow(self.hwnd):
                            self._create_window()
                        win32gui.ShowWindow(self.hwnd, win32con.SW_SHOW)
                    else:
                        win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)
                except Exception as e:
                    print(f"Failed to update window visibility: {e}")
                    self._create_window()
                    
        if stream_proof is not None:
            try:
                if stream_proof:
                    windll.user32.SetWindowDisplayAffinity(self.hwnd, 0x00000011)
                else:
                    windll.user32.SetWindowDisplayAffinity(self.hwnd, 0)
            except Exception as e:
                print(f"Failed to update stream proof setting: {e}")

        if size is not None:
            try:
                size_value = int(size)
                if 1 <= size_value <= 20:
                    self.size = size_value
            except ValueError:
                self.size = 8

        if thickness is not None:
            try:
                thickness_value = int(thickness)
                if 1 <= thickness_value <= 10:
                    self.thickness = thickness_value
            except ValueError:
                self.thickness = 1

        if type is not None:
            valid_types = ["cross", "dot", "x"]
            self.type = type.lower() if type.lower() in valid_types else "cross"

        if color is not None:
            try:
                color = color.lstrip('#')
                self.color = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
            except ValueError:
                pass

        self.update()
    
    def update(self):
        if self.hwnd and self.enabled:
            try:
                win32gui.InvalidateRect(self.hwnd, None, True)
                win32gui.UpdateWindow(self.hwnd)
            except Exception as e:
                print(f"Failed to update window: {e}")
                self._create_window()

    def wndProc(self, hwnd, msg, wParam, lParam):
        if msg == win32con.WM_PAINT and self.enabled:
            try:
                hdc, ps = win32gui.BeginPaint(hwnd)
                rect = win32gui.GetClientRect(hwnd)
                
                mem_dc = win32gui.CreateCompatibleDC(hdc)
                bitmap = win32gui.CreateCompatibleBitmap(hdc, rect[2], rect[3])
                old_bitmap = win32gui.SelectObject(mem_dc, bitmap)

                brush = win32gui.CreateSolidBrush(win32api.RGB(0, 0, 0))
                win32gui.FillRect(mem_dc, rect, brush)
                win32gui.DeleteObject(brush)

                win32gui.SetBkMode(mem_dc, win32con.TRANSPARENT)
                color = win32api.RGB(*self.color)
                
                # Scale down thickness to keep it reasonable
                pen_thickness = max(1, int(self.thickness * 0.2))
                pen = win32gui.CreatePen(win32con.PS_SOLID, pen_thickness, color)
                brush = win32gui.CreateSolidBrush(color)
                
                old_pen = win32gui.SelectObject(mem_dc, pen)
                old_brush = win32gui.SelectObject(mem_dc, brush)

                center_x = rect[2] // 2
                center_y = rect[3] // 2

                if self.type == "dot":
                    self.draw_dot(mem_dc, center_x, center_y)
                elif self.type == "cross":
                    self.draw_crosshair(mem_dc, center_x, center_y)
                elif self.type == "x":
                    self.draw_x(mem_dc, center_x, center_y)

                win32gui.BitBlt(hdc, 0, 0, rect[2], rect[3], mem_dc, 0, 0, win32con.SRCCOPY)

                win32gui.SelectObject(mem_dc, old_pen)
                win32gui.SelectObject(mem_dc, old_brush)
                win32gui.DeleteObject(pen)
                win32gui.DeleteObject(brush)
                win32gui.SelectObject(mem_dc, old_bitmap)
                win32gui.DeleteObject(bitmap)
                win32gui.DeleteDC(mem_dc)

                win32gui.EndPaint(hwnd, ps)
                return 0
            except Exception as e:
                print(f"Error in wndProc: {e}")
                return 0
        
        return win32gui.DefWindowProc(hwnd, msg, wParam, lParam)

    def draw_dot(self, dc, x, y):
        radius = int(self.thickness * 0.8)
        win32gui.Ellipse(dc, x - radius, y - radius, x + radius, y + radius)

    def draw_crosshair(self, dc, x, y):
        size = self.size
        thickness = max(1, int(self.thickness * 0.3))
        half_thick = thickness // 2
        points = [
            (x - size, y - half_thick),
            (x - size, y + half_thick),
            (x + size, y + half_thick),
            (x + size, y - half_thick)
        ]
        win32gui.Polygon(dc, points)

        points = [
            (x - half_thick, y - size),
            (x - half_thick, y + size),
            (x + half_thick, y + size),
            (x + half_thick, y - size)
        ]
        win32gui.Polygon(dc, points)

    def draw_x(self, dc, x, y):
        size = self.size
        thickness = max(1, int(self.thickness * 0.3))
        half_thick = thickness // 2
        angle = 45  # degrees
        rad = math.radians(angle)
        cos_a = math.cos(rad)
        sin_a = math.sin(rad)
        x1 = int(x - size * cos_a)
        y1 = int(y - size * sin_a)
        x2 = int(x + size * cos_a)
        y2 = int(y + size * sin_a)

        points1 = [
            (int(x1 - half_thick * sin_a), int(y1 + half_thick * cos_a)),
            (int(x1 + half_thick * sin_a), int(y1 - half_thick * cos_a)),
            (int(x2 + half_thick * sin_a), int(y2 - half_thick * cos_a)),
            (int(x2 - half_thick * sin_a), int(y2 + half_thick * cos_a))
        ]
        win32gui.Polygon(dc, points1)
        x3 = int(x + size * cos_a)
        y3 = int(y - size * sin_a)
        x4 = int(x - size * cos_a)
        y4 = int(y + size * sin_a)

        points2 = [
            (int(x3 - half_thick * sin_a), int(y3 - half_thick * cos_a)),
            (int(x3 + half_thick * sin_a), int(y3 + half_thick * cos_a)),
            (int(x4 + half_thick * sin_a), int(y4 + half_thick * cos_a)),
            (int(x4 - half_thick * sin_a), int(y4 - half_thick * cos_a))
        ]
        win32gui.Polygon(dc, points2)
        
    def __del__(self):
        if self.hwnd and win32gui.IsWindow(self.hwnd):
            try:
                win32gui.DestroyWindow(self.hwnd)
                win32gui.UnregisterClass(self.class_name, None)
            except:
                pass