import win32gui
import win32process
import psutil
import win32api
import configparser
import os
from DefaultSettings import *

def is_fullscreen(hwnd):
    # More flexible fullscreen detection
    monitor = win32api.MonitorFromWindow(hwnd)
    monitor_info = win32api.GetMonitorInfo(monitor)
    monitor_area = monitor_info.get("Monitor")
    window_rect = win32gui.GetWindowRect(hwnd)
    
    # Check if window dimensions are close to monitor dimensions (allow small differences)
    mon_width = monitor_area[2] - monitor_area[0]
    mon_height = monitor_area[3] - monitor_area[1]
    win_width = window_rect[2] - window_rect[0]
    win_height = window_rect[3] - window_rect[1]
    
    # If window size is within 10 pixels of monitor size, consider it fullscreen
    return (abs(win_width - mon_width) <= 10 and abs(win_height - mon_height) <= 10)

def is_aimer_menu_active():
    try:
        hwnd = win32gui.GetForegroundWindow()
        _, pid = win32process.GetWindowThreadProcessId(hwnd)
        process = psutil.Process(pid)
        return process.name().lower().startswith("vanguard")
    except:
        return False
    
def get_supported_games():
    ini_path = os.path.join(os.path.expanduser('~'), MAIN_FOLDER, 'assets', 'games', 'target.ini')
    
    if not os.path.exists(ini_path):
        return []
    
    config = configparser.ConfigParser()
    try:
        config.read(ini_path)
        supported_games = []
        
        for section in config.sections():
            if 'target_exe' in config[section]:
                game = config[section]['target_exe'].lower()
                supported_games.append(game)
        
        return supported_games
    except Exception as e:
        return []


def is_supported_game_active():
    try:
        hwnd = win32gui.GetForegroundWindow()
        _, pid = win32process.GetWindowThreadProcessId(hwnd)
        process = psutil.Process(pid)
        current_process = process.name().lower()
        
        supported_games = get_supported_games()
        
        if current_process in supported_games:
            is_full = is_fullscreen(hwnd)
            return is_full
        return False
    except Exception as e:
        return False

def is_on_main_monitor():
    x, y = win32api.GetCursorPos()
    monitor_info = win32api.GetMonitorInfo(win32api.MonitorFromPoint((0,0)))
    monitor_area = monitor_info.get("Monitor")
    return monitor_area[0] <= x <= monitor_area[2] and monitor_area[1] <= y <= monitor_area[3]

def can_perform_action(gui_instance=None):
    try:
        game_active = is_supported_game_active()
        aimer_menu_active = is_aimer_menu_active()
        on_main_monitor = is_on_main_monitor()
        
        if gui_instance and gui_instance.is_mouse_over_gui():
            return True
        
        return (game_active or aimer_menu_active) and on_main_monitor
    except Exception as e:
        return False