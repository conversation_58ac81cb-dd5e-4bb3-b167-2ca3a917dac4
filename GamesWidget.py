import flet as ft
from DefaultSettings import *
import configparser
import os
from pathlib import Path
import json


def load_data_from_ini(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        data = []
        current_section = None
        current_details = []
        
        # Status-Mapping für einfachere Handhabung
        status_colors = {
            "working": WORKING_COLOR,
            "updating": UPDATING_COLOR,
            "detected": DETECTED_COLOR
        }
        
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line:
                if line.startswith('[') and line.endswith(']'):
                    # Wenn wir vorherige Daten haben, fügen wir sie hinzu
                    if current_section is not None:
                        data.append((
                            current_section, 
                            status, 
                            status_colors.get(status.lower(), DETECTED_COLOR),
                            current_details
                        ))
                    
                    # Neue Sektion starten
                    current_section = line[1:-1]
                    current_details = []
                elif line.startswith('status'):
                    status = line.split('=')[1].strip()
                elif line.startswith('details'):
                    continue
                elif '>' in line:
                    current_details.append(line)
        
        # Letzte Sektion hinzufügen
        if current_section is not None:
            data.append((
                current_section, 
                status, 
                status_colors.get(status.lower(), DETECTED_COLOR),
                current_details
            ))
        
        return data
        
    except Exception as e:
        print(f"Error loading INI file: {e}")
        return []

def get_games_data():
    base_dir = Path(os.path.expanduser("~")) / MAIN_FOLDER
    games_ini_path = base_dir / 'assets' / 'games' / 'games.ini'
    return load_data_from_ini(games_ini_path)

def create_game_widget(game_name, status, color, image_path, details):
    def get_status_icon(status):
        status_icons = {
            "working": ft.icons.FINGERPRINT,
            "updating": ft.icons.SENSORS,
            "detected": ft.icons.MONITOR_HEART,
        }
        return status_icons.get(status.lower(), ft.icons.ERROR)

    # Icons für die verschiedenen Detail-Kategorien
    detail_icons = {
        "Status": get_status_icon(status),
        "Anti Cheat": ft.icons.SECURITY,
        "Launcher": ft.icons.ROCKET_LAUNCH,
        "Engine": ft.icons.MEMORY,
        "Developer": ft.icons.CODE,
        "Publisher": ft.icons.BUSINESS,
    }

    # Container für die Details mit Icons, Status zuerst
    details_content = [
        # Status als erste Zeile
        ft.Container(
            content=ft.Row(
                controls=[
                    ft.Container(
                        content=ft.Icon(
                            detail_icons["Status"],
                            size=14,
                            color=color,
                        ),
                        width=20,
                    ),
                    ft.Container(
                        content=ft.Text(
                            "Status",
                            color=color,
                            size=13,
                            weight=ft.FontWeight.BOLD,
                        ),
                        width=80,
                    ),
                    ft.Text(
                        status,
                        color=TEXT_COLOR,
                        size=13,
                    ),
                ],
                spacing=10,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
            ),
        )
    ]

    for detail in details:
        detail = detail.strip()
        if ">" in detail:
            category, value = [x.strip() for x in detail.split(">", 1)]
            if category in detail_icons:
                details_content.append(
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Container(
                                    content=ft.Icon(
                                        detail_icons[category],
                                        size=14,
                                        color=color,
                                    ),
                                    width=20,
                                ),
                                ft.Container(
                                    content=ft.Text(
                                        category,
                                        color=color,
                                        size=13,
                                        weight=ft.FontWeight.BOLD,
                                    ),
                                    width=80,
                                ),
                                ft.Text(
                                    value,
                                    color=TEXT_COLOR,
                                    size=13,
                                ),
                            ],
                            spacing=10,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                    )
                )

    details_container = ft.Container(
        content=ft.Column(
            controls=details_content,
            spacing=8,
        ),
        padding=ft.padding.only(left=60, top=10, bottom=10),
        visible=False,
    )

    def toggle_details(e):
        details_container.visible = not details_container.visible
        expand_icon.rotate = ft.transform.Rotate(angle=180 if details_container.visible else 0)
        e.page.update()

    expand_icon = ft.Icon(
        ft.icons.KEYBOARD_ARROW_DOWN,
        color=color,
        size=20,
        rotate=ft.transform.Rotate(0),
    )

    main_row = ft.Container(
        content=ft.Row(
            controls=[
                ft.Image(
                    src=image_path,
                    width=50,
                    height=50,
                    fit=ft.ImageFit.COVER,
                    border_radius=ft.border_radius.all(4),
                ),
                ft.Container(
                    content=ft.Text(
                        game_name,
                        size=14,
                        color=color,
                    ),
                    padding=ft.padding.only(left=10),
                ),
                ft.Container(expand=True),
                expand_icon,
            ],
            alignment=ft.MainAxisAlignment.START,
        ),
        padding=10,
        on_click=toggle_details,
    )

    return ft.Container(
        content=ft.Column(
            controls=[
                main_row,
                details_container,
            ],
            spacing=0,
        ),
        bgcolor=WIDGET_BG,
        border_radius=4,
        border=ft.border.all(1, BORDER_COLOR),
        width=700,
        margin=ft.margin.only(bottom=5),
    )

def create_game_card():
    games_data = get_games_data()
    base_dir = Path(os.path.expanduser("~")) / MAIN_FOLDER / "assets" / "ui"
    
    def get_game_image(game_name):
        png_path = base_dir / f"{game_name}.png"
        jpg_path = base_dir / f"{game_name}.jpg"
        
        if png_path.exists():
            return str(png_path)
        elif jpg_path.exists():
            return str(jpg_path)
        else:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            return os.path.join(current_dir, "Vanguard.ico")

    all_games = []
    for game_data in games_data:
        game, status, color, details = game_data
        all_games.append(
            create_game_widget(
                game,
                status,
                color,
                get_game_image(game),
                details
            )
        )

    return ft.Container(
        content=ft.Column(
            controls=all_games,
            scroll=ft.ScrollMode.AUTO,
            spacing=0,
            expand=True,
        ),
        padding=10,
        expand=True,
    )