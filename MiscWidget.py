import flet as ft
from DefaultSettings import *
import keyboard
from ConfigManager import Config<PERSON>ana<PERSON>
from AutomationWidget import <PERSON>BunnyHop, AutoPing, AutoSprint
from pynput import mouse
import threading
import time
import json
import os
from pathlib import Path

config_manager = ConfigManager()

def create_general_widget(page: ft.Page):
    widget_width = 350
    widget_margin = ft.margin.only(left=-15, right=0, top=-12, bottom=-12)
    
    # Hardcoded English translations
    translations = {
        "title": "Automation",
        "auto_bhop": "Auto Bunny Hop",
        "auto_ping": "Auto Ping",
        "auto_sprint": "Auto Sprint"
    }
    
    automation_state = {
        'auto_bhop': False,
        'auto_ping': False,
        'auto_sprint': False,
        'sprint_key': 'Left Shift'
    }

    auto_bhop = AutoBunnyHop()
    auto_ping = AutoPing()
    auto_sprint = AutoSprint()

    def create_toggle_switch(label, state_key):
        def toggle_switch_clicked(e=None):
            automation_state[state_key] = not automation_state[state_key]
            toggle_switch.controls[1].right = 0 if automation_state[state_key] else 20
            toggle_switch.controls[1].bgcolor = ACCENT_COLOR if automation_state[state_key] else ft.colors.GREY_400
            
            if state_key == "auto_bhop":
                auto_bhop.toggle(automation_state[state_key])
            elif state_key == "auto_ping":
                auto_ping.toggle(automation_state[state_key])
            elif state_key == "auto_sprint":
                auto_sprint.toggle(automation_state[state_key])
            
            try:
                toggle_switch.update()
            except Exception:
                pass

        toggle_switch = ft.Stack(
            controls=[
                ft.Container(
                    width=45,
                    height=18,
                    border_radius=12,
                    bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
                    animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                ),
                ft.Container(
                    width=18,
                    height=18,
                    right=20,
                    bgcolor=ft.colors.GREY_400,
                    border_radius=12,
                    animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                    on_click=lambda e: toggle_switch_clicked(e),
                )
            ]
        )

        return ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text(
                        label,
                        size=13,
                        color=TEXT_COLOR,
                    ),
                    ft.Container(expand=True),
                    toggle_switch,
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            ),
            padding=ft.padding.only(left=0, right=0, top=5, bottom=5),
        )

    def create_key_bind_container(page):
        is_binding = [False]
        
        bind_text = ft.Text(
            f"{automation_state['sprint_key']}", 
            size=13,
            color=ACCENT_COLOR,
            text_align=ft.TextAlign.CENTER,
        )

        bind_button = ft.Container(
            content=ft.Row(
                [bind_text],
                alignment=ft.MainAxisAlignment.CENTER,
            ),
            width=380,
            height=30,
            border_radius=4,
            bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
            border=ft.border.all(1, ft.colors.with_opacity(1, WIDGET_BG)),
            on_click=lambda e: start_binding(),
            padding=ft.padding.only(top=0),
        )

        def cleanup_binding():
            is_binding[0] = False
            bind_button.bgcolor = ft.colors.with_opacity(0.1, ACCENT_COLOR)
            keyboard.unhook_all()
            page.update()
            bind_button.update()

        def on_key_event(event):
            if is_binding[0]:
                scan_code = event.scan_code
                
                scan_code_map = {
                    42: "Left Shift",
                    54: "Right Shift",
                    29: "Left Control",
                    285: "Right Control",
                    56: "Left Alt",
                    312: "Right Alt",
                    57: "Space",
                    58: "Caps Lock",
                    15: "Tab"
                }
                
                if scan_code in scan_code_map:
                    key_pressed = scan_code_map[scan_code]
                else:
                    key_pressed = event.name.replace("umschalt", "shift").replace("strg", "ctrl").title()
                
                automation_state['sprint_key'] = key_pressed
                bind_text.value = f"{key_pressed}"

                if automation_state['auto_sprint']:
                    auto_sprint.set_sprint_key(key_pressed)

                cleanup_binding()
                page.update()

        def start_binding():
            if not is_binding[0]:
                is_binding[0] = True
                bind_text.value = translations["press_key"]
                bind_button.bgcolor = ft.colors.with_opacity(0.2, ACCENT_COLOR)
                keyboard.on_press(on_key_event)
                page.update()
                bind_button.update()

        return bind_button

    def reset_sprint_key(e):
        default_value = "Left Shift"
        automation_state['sprint_key'] = default_value
        key_bind_button.content.controls[0].value = f"{default_value}"
        
        if automation_state['auto_sprint']:
            auto_sprint.set_sprint_key(default_value)
        
        reset_key_button.content.color = ACCENT_COLOR
        reset_key_button.update()
        
        def color_reset_thread():
            time.sleep(0.5)
            reset_key_button.content.color = ft.colors.YELLOW
            reset_key_button.update()
        
        threading.Thread(target=color_reset_thread).start()
        
        key_bind_button.update()

    reset_key_button = ft.Container(
        content=ft.Icon(
            ft.icons.RESTART_ALT_ROUNDED,
            color=ft.colors.YELLOW,
            size=20,
        ),
        width=24,
        height=24,
        border_radius=4,
        on_click=reset_sprint_key,
        tooltip=translations["reset_tooltip"],
        ink=False,
    )

    key_bind_button = create_key_bind_container(page)
    
    key_container = ft.Container(
        content=ft.Column([
            ft.Text(
                translations["sprint_key"], 
                size=13,
                color=TEXT_COLOR,
            ),
            ft.Row([
                ft.Container(
                    content=key_bind_button,
                    padding=ft.padding.all(0),
                    border_radius=4,
                    border=ft.border.all(1, ft.colors.with_opacity(1, WIDGET_BG)),
                    bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
                    expand=True,
                ),
                reset_key_button,
            ]),
        ], spacing=5),
        padding=ft.padding.only(top=5, left=0),
    )

    # Erstelle die Toggle-Switches mit übersetzten Labels
    toggles = [
        create_toggle_switch(translations["auto_bhop"], "auto_bhop"),
        create_toggle_switch(translations["auto_ping"], "auto_ping"),
        create_toggle_switch(translations["auto_sprint"], "auto_sprint"),
    ]

    
    main_container = ft.Container(
        content=ft.Column(
            controls=[
                ft.Container(
                    content=ft.Column([
                        ft.Row(
                            controls=[
                                ft.Icon(
                                    ft.Icons.PRECISION_MANUFACTURING,
                                    color=ACCENT_COLOR,
                                    size=TITLE_SIZE,
                                ),
                                ft.Text(
                                    translations["title"],
                                    size=TITLE_SIZE,
                                    weight=ft.FontWeight.BOLD,
                                    color=ACCENT_COLOR,
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.START,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                        ft.Container(
                            ft.Divider(color=ft.Colors.with_opacity(0.1, ACCENT_COLOR), height=1),
                            margin=ft.margin.only(top=3, bottom=2),
                        ),
                    ]),
                    padding=ft.padding.only(left=8, right=8, top=0)
                ),
                ft.Container(height=3),
                ft.Container(
                    content=ft.Column(
                        controls=toggles + [key_container],
                        spacing=3
                    ),
                    padding=ft.padding.symmetric(horizontal=12),
                    expand=True,
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.START,
            spacing=5,
            expand=True,
        ),
        padding=ft.padding.only(left=0, top=12, bottom=12, right=0),
        margin=widget_margin,
        bgcolor=WIDGET_BG,
        border_radius=4,
        border=ft.border.all(1, BORDER_COLOR),
        width=widget_width,
    )


    def get_settings():
        return {
            'auto_bunny_hop': automation_state['auto_bhop'],
            'auto_ping': automation_state['auto_ping'],
            'auto_sprint': automation_state['auto_sprint'],
            'sprint_key': automation_state['sprint_key']
        }

    def load_settings(settings):
        if settings:
            automation_state['auto_bhop'] = settings.get('auto_bunny_hop', False)
            automation_state['auto_ping'] = settings.get('auto_ping', False)
            automation_state['auto_sprint'] = settings.get('auto_sprint', False)
            automation_state['sprint_key'] = settings.get('sprint_key', 'Left Shift')
            
            for toggle in main_container.content.controls[2].content.controls[:3]:
                label = toggle.content.controls[0].value
                state_key_map = {
                    translations["auto_bhop"]: "auto_bhop",
                    translations["auto_ping"]: "auto_ping",
                    translations["auto_sprint"]: "auto_sprint"
                }
                state_key = state_key_map.get(label)
                if state_key:
                    toggle.content.controls[2].controls[1].right = 0 if automation_state[state_key] else 20
                    toggle.content.controls[2].controls[1].bgcolor = ACCENT_COLOR if automation_state[state_key] else ft.colors.GREY_400
            
            key_bind_button.content.controls[0].value = f"{automation_state['sprint_key']}"
            
            auto_bhop.toggle(automation_state['auto_bhop'])
            auto_ping.toggle(automation_state['auto_ping'])
            auto_sprint.toggle(automation_state['auto_sprint'])
            if automation_state['auto_sprint']:
                auto_sprint.set_sprint_key(automation_state['sprint_key'])
            
            page.update()

    def update_translations(new_language):
        # No need to update translations since we're using English only
        pass
        
        # Update reset tooltip
        reset_key_button.tooltip = translations["reset_tooltip"]
        
        # Nur update aufrufen, wenn das Widget der Seite bereits hinzugefügt wurde
        if hasattr(main_container, '_Control__page') and main_container._Control__page is not None:
            main_container.update()
            
    setattr(main_container, 'get_settings', get_settings)
    setattr(main_container, 'load_settings', load_settings)
    setattr(main_container, 'update_translations', update_translations)
    return main_container

def create_config_widget(page: ft.Page):
    widget_width = 350
    widget_margin = ft.margin.only(left=345, right=-20, top=-12, bottom=-12)
    
    # Hardcoded English translations
    translations = {
        "title": "Configuration Manager",
        "enter_name": "Enter configuration name...",
        "save": "Save",
        "load": "Load",
        "delete": "Delete",
        "reset": "Reset"
    }

    config_name_input = ft.TextField(
        width=390,
        height=30,
        bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
        color=TEXT_COLOR,
        border_color=ft.colors.with_opacity(1, WIDGET_BG),
        focused_border_color=ACCENT_COLOR,  
        cursor_color=ACCENT_COLOR,
        text_size=13,
        content_padding=ft.padding.all(8),
        hint_text=translations["enter_name"],
        border_width=1,  
        focused_border_width=1, 
    )

    configs_list = ft.ListView(
        height=220,
        spacing=2,
        padding=5,
        controls=[]
    )

    def create_config_item(config_name):
        return ft.Container(
            content=ft.Row([
                ft.Text(
                    config_name,
                    size=TEXT_SIZE,  
                    color=ACCENT_COLOR,
                ),
                ft.Container(expand=True),
                ft.IconButton(
                    icon=ft.icons.CLOSE,
                    icon_color=ft.colors.RED_400,
                    icon_size=TITLE_SIZE,
                    tooltip=translations["delete_config"],
                    on_click=lambda e, name=config_name: delete_config(e, name),
                    style=ft.ButtonStyle(
                        padding=0,
                        animation_duration=0,
                        overlay_color=ft.colors.TRANSPARENT,
                    ),
                )
            ]),
            bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
            border_radius=4,
            padding=ft.padding.only(left=8, right=0, top=0, bottom=0),
            height=25, 
            on_click=lambda e, name=config_name: load_config(name),
        )

    def refresh_configs_list():
        configs = config_manager.get_config_files()
        new_controls = [create_config_item(name) for name in configs]
        configs_list.controls = new_controls
        try:
            widget.update()
        except:
            pass

    def delete_config(e, config_name):
        config_manager.delete_config(config_name)
        if config_name_input.value == config_name:
            config_name_input.value = ""
        
        current_config = config_manager.get_current_config()
        if current_config == config_name:
            config_manager.save_current_config("")
            
        refresh_configs_list()

    def load_config(config_name):
        config_name_input.value = config_name
        settings = config_manager.load_config(config_name)
        
        if settings:
            try:
                if 'crosshair' in settings and hasattr(page, 'crosshair_widget'):
                    crosshair_settings = settings['crosshair']
                    # Prüfe ob das crosshair_widget das richtige Format hat
                    if hasattr(page.crosshair_widget, 'load_settings'):
                        if hasattr(page, 'crosshair'):
                            page.crosshair.update_settings(enabled=False)
                        page.crosshair_widget.load_settings(crosshair_settings)
                
                if 'general' in settings and hasattr(page, 'general_widget'):
                    general_settings = settings['general']
                    if hasattr(page.general_widget, 'load_settings'):
                        page.general_widget.load_settings(general_settings)
                
                if 'rapidfire' in settings and hasattr(page, 'rapidfire_widget'):
                    rapidfire_settings = settings['rapidfire']
                    if hasattr(page.rapidfire_widget, 'load_settings'):
                        page.rapidfire_widget.load_settings(rapidfire_settings)
                
                if 'recoil' in settings and 'primary' in settings['recoil'] and hasattr(page, 'primary_recoil'):
                    recoil_settings = settings['recoil']['primary']
                    if hasattr(page.primary_recoil, 'load_settings'):
                        page.primary_recoil.load_settings(recoil_settings)
                
                page.update()
                
            except Exception as e:
                print(f"Error loading settings: {e}")
                # Debug-Informationen
                print(f"Settings content: {settings}")
                import traceback
                traceback.print_exc()

    def on_save_clicked(e):
        config_name = config_name_input.value
        if not config_name:
            return

        settings = {}
        if hasattr(page, 'general_widget'):
            settings['general'] = page.general_widget.get_settings()
        if hasattr(page, 'primary_recoil'):
            settings['recoil'] = {'primary': page.primary_recoil.get_settings()}
        if hasattr(page, 'rapidfire_widget'):
            settings['rapidfire'] = page.rapidfire_widget.get_settings()
        if hasattr(page, 'crosshair_widget'):
            settings['crosshair'] = page.crosshair_widget.get_settings()

        if config_manager.save_config(config_name, settings):
            config_manager.save_current_config(config_name)
            refresh_configs_list()

    widget = ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.SETTINGS_APPLICATIONS, color=ACCENT_COLOR, size=TITLE_SIZE),
                        ft.Text(
                            translations["title"],
                            size=TITLE_SIZE,
                            weight=ft.FontWeight.BOLD,
                            color=ACCENT_COLOR
                        ),
                    ]),
                    ft.Container(
                        ft.Divider(color=ft.colors.with_opacity(0.1, ACCENT_COLOR), height=1),
                        margin=ft.margin.only(top=3, bottom=2),
                    ),
                ]),
                padding=ft.padding.only(left=8, right=8, top=0)
            ),
            ft.Container(height=3),
            ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Column([
                            ft.Text(translations["config_name"], size=TEXT_SIZE, color=TEXT_COLOR),
                            config_name_input
                        ], spacing=5),
                        padding=ft.padding.only(top=5),
                    ),
                    ft.Container(height=10),
                    ft.Text(translations["available_configs"], size=TEXT_SIZE, color=TEXT_COLOR),
                    ft.Container(
                        content=configs_list,
                        bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
                        border_radius=4,
                        border=ft.border.all(1, ft.colors.with_opacity(0.25, BORDER_COLOR)),
                    ),
                    ft.Container(
                        content=ft.Row([
                            ft.ElevatedButton(
                                content=ft.Text(
                                    translations["save_config"],
                                    size=TEXT_SIZE,
                                    weight=ft.FontWeight.NORMAL,
                                    color=ACCENT_COLOR
                                ),
                                width=320,
                                height=30,
                                on_click=on_save_clicked,
                                style=ft.ButtonStyle(
                                    bgcolor={ft.ControlState.DEFAULT: ft.colors.with_opacity(0.1, ACCENT_COLOR)},
                                    shape=ft.RoundedRectangleBorder(radius=4),
                                    overlay_color=ft.colors.TRANSPARENT,
                                ),
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        padding=ft.padding.only(top=5),
                    ),
                ], spacing=3),
                padding=ft.padding.symmetric(horizontal=12),
                expand=True,
            ),
        ]),
        width=widget_width,
        margin=widget_margin,
        padding=ft.padding.only(left=4, top=12, bottom=12, right=0),
        bgcolor=WIDGET_BG,
        border_radius=4,
        border=ft.border.all(1, BORDER_COLOR),
    )

    def load_current_config():
        try:
            current_config = config_manager.get_current_config()
            if current_config:
                config_name_input.value = current_config
                settings = config_manager.load_config(current_config)
                
                if settings and hasattr(page, 'general_widget'):
                    page.general_widget.load_settings(settings['general'])
            
            configs = config_manager.get_config_files()
            configs_list.controls = [create_config_item(name) for name in configs]
            
        except Exception as e:
            print(f"Error loading current config: {e}")

    def update_translations(new_language):
        # No need to update translations since we're using English only
        pass

    widget.load_current_config = load_current_config
    widget.update_translations = update_translations
    
    return widget

def create_misc_layout(page: ft.Page):
    try:
        # Erstelle die Widgets
        general_widget = create_general_widget(page)
        config_widget = create_config_widget(page)
        
        # Weise sie der Seite zu
        page.general_widget = general_widget
        page.config_widget = config_widget
        
        # Erstelle das Layout
        layout = ft.Container(
            content=ft.Stack(
                controls=[
                    general_widget,
                    config_widget,
                ],
            ),
        )
        
        # Weise das Layout der Seite zu
        page.misc_layout = layout
        
        # Versuche die Konfiguration zu laden
        try:
            if hasattr(page.config_widget, 'load_current_config'):
                page.config_widget.load_current_config()
            if hasattr(page.general_widget, 'get_settings'):
                page.general_widget.get_settings()
        except Exception as e:
            print(f"Error loading configuration: {e}")
        
        return layout
        
    except Exception as e:
        print(f"Error creating misc layout: {e}")
        return ft.Container()