import win32api
import win32con
import threading
import time
import ctypes
from GameChecker import is_supported_game_active, can_perform_action
from pynput.mouse import <PERSON><PERSON>, Controller
from pynput.mouse import <PERSON><PERSON>, Controller, Listener 

from XboxRecoil import XboxController

class RecoilSystem:
    def __init__(self):
        self.controller_mode = False
        self.controller = None
        self.running = True
        self.paused = False
        self.mouse_pressed = False
        self.lock = threading.Lock()
        self.mouse_controller = Controller()
        self.recoil_enabled = False
        self.use_secondary_recoil = False
        self.mouse_listener = None
        self.fire_key = "Mouse Left" 
        self.use_controller = False  # Neue Variable
        self.xbox_controller = None  # Controller Instance
        
        # Recoil Patterns
        self.recoil_pattern = [(0.0, 0.0, 0.0)]
        self.secondary_recoil_pattern = [(0.0, 0.0, 0.0)]
        self.accumulated_x = 0.0
        self.accumulated_y = 0.0
        
        self.setup_mouse_listener()
    
    def set_controller_mode(self, enabled: bool):
        self.controller_mode = enabled
        with self.lock:
            self.use_controller = enabled
            if enabled:
                # Deaktiviere Maus-Listener
                if self.mouse_listener:
                    self.mouse_listener.stop()
                    self.mouse_listener = None
                
                # Aktiviere Controller
                if not self.xbox_controller:
                    from XboxRecoil import XboxController
                    self.xbox_controller = XboxController()
            else:
                # Deaktiviere Controller
                if self.xbox_controller:
                    self.xbox_controller.stop()
                    self.xbox_controller = None
                
                # Reaktiviere Maus-Listener
                self.setup_mouse_listener()

    def set_fire_key(self, key):
        """Setzt den Feuer-Tastenschlüssel für beide Profile synchron"""
        with self.lock:
            self.fire_key = key
            self.mouse_pressed = False

    def move_mouse_relative(self, dx, dy):
        actual_dx = dx / 100.0 
        actual_dy = dy / 100.0
        remainder_x = actual_dx - int(actual_dx)
        remainder_y = actual_dy - int(actual_dy)
        self.accumulated_x = getattr(self, 'accumulated_x', 0) + remainder_x
        self.accumulated_y = getattr(self, 'accumulated_y', 0) + remainder_y
        move_x = int(actual_dx) + int(self.accumulated_x)
        move_y = int(actual_dy) + int(self.accumulated_y)
        self.accumulated_x -= int(self.accumulated_x)
        self.accumulated_y -= int(self.accumulated_y)
        ctypes.windll.user32.mouse_event(0x0001, move_x, move_y, 0, 0)

    def handle_mouse_click(self, button, pressed):
        with self.lock:
            if self.fire_key == "Mouse Left" and button == Button.left:
                self.mouse_pressed = pressed
            elif self.fire_key == "Mouse Right" and button == Button.right:
                self.mouse_pressed = pressed


    def check_keys_and_update(self):
        if win32api.GetAsyncKeyState(ord('1')) & 0x8000:
            self.use_secondary_recoil = False
            return True
        
        elif win32api.GetAsyncKeyState(ord('2')) & 0x8000:
            self.use_secondary_recoil = True
            return True
            
        return False

    def pause(self):
        with self.lock:
            self.paused = True

    def resume(self):
        with self.lock:
            self.paused = False
            
    def apply_recoil(self):
        while self.running:
            if not self.use_controller:  # Nur Maus-Recoil
                self.check_keys_and_update()
                
                with self.lock:
                    is_running = self.running
                    is_paused = self.paused
                    is_mouse_pressed = self.mouse_pressed
                    is_enabled = self.recoil_enabled
                    current_fire_key = self.fire_key
                
                if not is_supported_game_active() or not can_perform_action():
                    time.sleep(0.01)
                    continue
                    
                if is_running and not is_paused and is_mouse_pressed and is_enabled:
                    pattern = self.secondary_recoil_pattern if self.use_secondary_recoil else self.recoil_pattern
                    for x, y, delay in pattern:
                        with self.lock:
                            if current_fire_key != self.fire_key:
                                break
                            if not (self.running and not self.paused and self.mouse_pressed and self.recoil_enabled) or \
                               not is_supported_game_active() or not can_perform_action():
                                break
                        self.move_mouse_relative(x, y)
                        time.sleep(delay / 1000.0)

            time.sleep(0.001)

    def setup_mouse_listener(self):
        def on_click(x, y, button, pressed):
            self.handle_mouse_click(button, pressed)

        self.mouse_listener = Listener(on_click=on_click)
        self.mouse_listener.start()


    def start(self):
        if not hasattr(self, 'recoil_thread') or not self.recoil_thread.is_alive():
            self.recoil_thread = threading.Thread(target=self.apply_recoil)
            self.recoil_thread.daemon = True
            self.recoil_thread.start()

    def stop(self):
        self.running = False
        if self.mouse_listener:
            self.mouse_listener.stop()
        if self.xbox_controller:
            self.xbox_controller.stop()
        if hasattr(self, 'recoil_thread'):
            self.recoil_thread.join()

    def set_recoil_enabled(self, enabled):
        self.recoil_enabled = enabled

    def set_recoil_patterns(self, primary_pattern, secondary_pattern):
        self.recoil_pattern = primary_pattern
        self.secondary_recoil_pattern = secondary_pattern
        
        # Wenn Controller aktiv ist, update die Controller-Werte
        if self.use_controller and self.xbox_controller:
            pattern = secondary_pattern if self.use_secondary_recoil else primary_pattern
            if pattern:
                horizontal, vertical, smoothing = pattern[0]
                self.xbox_controller.set_recoil_values(horizontal, vertical, smoothing)

    def handle_mouse_click(self, button, pressed):
        with self.lock:
            if (self.fire_key == "Mouse Left" and button == Button.left) or \
               (self.fire_key == "Mouse Right" and button == Button.right):
                self.mouse_pressed = pressed
            else:
                self.mouse_pressed = False