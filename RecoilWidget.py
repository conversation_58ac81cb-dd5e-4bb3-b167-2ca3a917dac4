import flet as ft
from DefaultSettings import *
import time
import json
import os
from pathlib import Path
import threading
from pynput import mouse
from MouseRecoil import RecoilSystem
from rapidfire import RapidF<PERSON>


def create_rapidfire_widget(page: ft.Page, current_language="en"):
    widget_width = 350
    widget_margin = ft.margin.only(left=345, right=-20, top=-12, bottom=-12)
    
    def get_translated_text(key, current_language="en"):
        try:
            base_dir = Path(os.path.expanduser("~")) / MAIN_FOLDER
            lang_path = base_dir / "assets" / "languages" / "LANGUAGES.json"
            
            if not os.path.exists(lang_path):
                return get_default_text(key)
                
            with open(lang_path, 'r', encoding='utf-8') as f:
                languages = json.load(f)
                
            if current_language in languages and "settings" in languages[current_language]:
                if key in languages[current_language]["settings"]["rapidfire"]:
                    return languages[current_language]["settings"]["rapidfire"][key]
        except Exception as e:
            print(f"Translation error: {e}")
        return get_default_text(key)

    def get_default_text(key):
        defaults = {
            "title": "Rapid Fire",
            "fire_delay": "Fire Delay",
            "activation_key": "Activation Key",
            "press_key": "Press a Key...",
            "reset_tooltip": "Reset to default"
        }
        return defaults.get(key, "")

    def update_translations(new_language):
        # Update title
        title_text.value = get_translated_text("title", new_language)
        
        # Update fire delay text
        fire_delay_text.value = get_translated_text("fire_delay", new_language)
        
        # Update activation key text
        activation_key_text.value = get_translated_text("activation_key", new_language)
        
        # Update bind button text if in binding mode
        bind_text = key_bind_button.content.controls[0]
        if bind_text.value == "Press a Key...":
            bind_text.value = get_translated_text("press_key", new_language)
            
        # Update reset tooltips
        reset_key_button.tooltip = get_translated_text("reset_tooltip", new_language)
        
        if main_container in page.controls:
            main_container.update()
        
    title_text = ft.Text(
        get_translated_text("title", current_language),
        size=TITLE_SIZE,
        weight=ft.FontWeight.BOLD,
        color=ACCENT_COLOR,
    )
    
    fire_delay_text = ft.Text(
        get_translated_text("fire_delay", current_language),
        size=TEXT_SIZE,
        color=TEXT_COLOR,
    )
    
    activation_key_text = ft.Text(
        get_translated_text("activation_key", current_language),
        size=TEXT_SIZE,
        color=TEXT_COLOR,
    )
    widget_width = 350
    widget_margin = ft.margin.only(left=345, right=-20, top=-12, bottom=-12)
    
    rapidfire_state = {
        'enabled': False,
        'click_delay': 2,
        'key': "X"
    }
    
    rapidfire = RapidFire()
    rapidfire.start()

    def toggle_switch_clicked(e):
        rapidfire_state['enabled'] = not rapidfire_state['enabled']
        toggle_switch.controls[1].right = 0 if rapidfire_state['enabled'] else 20
        toggle_switch.controls[1].bgcolor = ACCENT_COLOR if rapidfire_state['enabled'] else ft.colors.GREY_400
        
        rapidfire.set_rapidfire_settings(
            rapidfire_state['enabled'],
            rapidfire_state['key'],
            rapidfire_state['click_delay']
        )
        
        toggle_switch.update()
        
        if hasattr(page, 'update_status_overlay'):
            page.update_status_overlay()
            
    toggle_switch = ft.Stack(
        controls=[
            ft.Container(
                width=45,
                height=18,
                border_radius=12,
                bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
                animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
            ),
            ft.Container(
                width=18,
                height=18,
                right=20,
                bgcolor=ft.colors.GREY_400,
                border_radius=12,
                animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                on_click=lambda e: toggle_switch_clicked(e),
            )
        ]
    )

    def create_delay_slider():
        display_value = rapidfire_state['click_delay']
        
        value_display = ft.TextField(
            value=str(display_value),
            width=42,
            height=22,
            text_size=TEXT_SIZE,
            color=ACCENT_COLOR,
            border_color=ft.colors.TRANSPARENT,
            focused_border_color=ACCENT_COLOR,
            cursor_color=ACCENT_COLOR,
            content_padding=ft.padding.all(4),
            text_align=ft.TextAlign.CENTER,
            keyboard_type=ft.KeyboardType.NUMBER,
            border_radius=4,
            bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
        )

        def on_change_text(e):
            new_text = ''.join(filter(str.isdigit, e.data))
            if new_text != e.data:
                value_display.value = new_text
                value_display.update()
        
        def validate_and_update_value(new_value):
            if new_value == "":
                new_value = "0"
            
            num_value = int(new_value)
            if num_value < 1:
                num_value = 1
            elif num_value > 10:
                num_value = 10
            
            rapidfire_state['click_delay'] = num_value
            slider.value = num_value
            value_display.value = str(num_value)
            
            if rapidfire_state['enabled']:
                rapidfire.set_rapidfire_settings(
                    rapidfire_state['enabled'],
                    rapidfire_state['key'],
                    num_value
                )
            
            value_display.update()
            slider.update()

        def on_submit(e):
            validate_and_update_value(e.control.value)

        def reset_value(e):
            default_value = 2
            rapidfire_state['click_delay'] = default_value
            slider.value = default_value
            value_display.value = str(default_value)
            
            if rapidfire_state['enabled']:
                rapidfire.set_rapidfire_settings(
                    rapidfire_state['enabled'],
                    rapidfire_state['key'],
                    default_value
                )
            
            reset_button.content.color = ACCENT_COLOR
            reset_button.update()
            
            def color_reset_thread():
                time.sleep(0.5)
                reset_button.content.color = ft.colors.YELLOW
                reset_button.update()
            
            threading.Thread(target=color_reset_thread).start()
            
            value_display.update()
            slider.update()

        reset_button = ft.Container(
            content=ft.Icon(
                ft.icons.RESTART_ALT_ROUNDED,
                color=ft.colors.YELLOW,
                size=20,
            ),
            width=24,
            height=24,
            border_radius=4,
            on_click=reset_value,
            tooltip="Reset to default",
            ink=False,
        )

        value_display.on_change = on_change_text
        value_display.on_submit = on_submit

        slider = ft.Slider(
            min=0,
            max=10,
            value=display_value,
            active_color=ACCENT_COLOR,
            inactive_color=ft.colors.with_opacity(0.1, ACCENT_COLOR),
            on_change=lambda e: validate_and_update_value(str(int(e.control.value))),
            expand=True,
            height=12,
            width=420,
            divisions=10,
            thumb_color=ACCENT_COLOR,
            adaptive=True,
            round=0,
        )

        container = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(
                        get_translated_text("fire_delay", current_language),  # Direkt den übersetzten Text verwenden
                        size=TEXT_SIZE,
                        color=TEXT_COLOR,
                    ),
                    ft.Container(expand=True),
                    value_display,
                ]),
                ft.Container(height=5),
                ft.Row([
                    ft.Container(
                        content=slider,
                        padding=ft.padding.only(left=-20, right=15),
                        expand=True,
                    ),
                    reset_button,
                ]),
            ], spacing=0),
            padding=ft.padding.only(left=0, top=2, bottom=2),
        )
        
        return container, slider, value_display

    delay_container, delay_slider, delay_value = create_delay_slider()

    def reset_key(e):
        default_value = "X"
        rapidfire_state['key'] = default_value
        
        # Get the bind text control and update its value
        bind_text = key_bind_button.content.controls[0]
        bind_text.value = f"{default_value}"
        
        if rapidfire_state['enabled']:
            rapidfire.set_rapidfire_settings(
                rapidfire_state['enabled'],
                default_value,
                rapidfire_state['click_delay']
            )
        
        reset_key_button.content.color = ACCENT_COLOR
        
        # Only update if the page reference exists
        if hasattr(page, 'update'):
            reset_key_button.update()
            key_bind_button.update()
        
        def color_reset_thread():
            time.sleep(0.5)
            reset_key_button.content.color = ft.colors.YELLOW
            if hasattr(page, 'update'):
                reset_key_button.update()
        
        threading.Thread(target=color_reset_thread).start()

    reset_key_button = ft.Container(
        content=ft.Icon(
            ft.icons.RESTART_ALT_ROUNDED,
            color=ft.colors.YELLOW,
            size=20,
        ),
        width=24,
        height=24,
        border_radius=4,
        on_click=reset_key,
        tooltip="Reset to default",
        ink=False,
    )

    def on_key_change(e):
        rapidfire_state['key'] = e.control.value
        if rapidfire_state['enabled']:
            rapidfire.set_rapidfire_settings(
                rapidfire_state['enabled'],
                e.control.value,
                rapidfire_state['click_delay']
            )

    def create_key_bind_container(page):
        is_binding = [False]
        
        bind_text = ft.Text(
            f"{rapidfire_state['key']}", 
            size=TEXT_SIZE,
            color=ACCENT_COLOR,
            text_align=ft.TextAlign.CENTER,
        )

        bind_button = ft.Container(
            content=ft.Row(
                [bind_text],
                alignment=ft.MainAxisAlignment.CENTER,
            ),
            width=330,
            height=25,
            border_radius=4,
            bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
            border=ft.border.all(0, ft.colors.with_opacity(0, WIDGET_BG)),
            on_click=lambda e: start_binding(),
            padding=ft.padding.only(top=0),
        )

        def cleanup_binding():
            """Reset binding state and remove event listener."""
            is_binding[0] = False
            bind_button.bgcolor = ft.colors.with_opacity(0.1, ACCENT_COLOR)
            page.on_keyboard_event = None 
            page.update()
            bind_button.update()

        def on_key(e: ft.KeyboardEvent):
            """Capture key and exit binding mode."""
            if is_binding[0]:
                try:
                    key_data = json.loads(e.data) if isinstance(e.data, str) else e.data 
                    key_pressed = key_data.get("key", "Unknown") 
                except json.JSONDecodeError:
                    key_pressed = str(e.data)

                if key_pressed.lower().startswith("mouse"):
                    cleanup_binding()
                    return

                rapidfire_state['key'] = key_pressed
                bind_text.value = f"{key_pressed}"

                if rapidfire_state['enabled']:
                    rapidfire.set_rapidfire_settings(
                        rapidfire_state['enabled'],
                        key_pressed,
                        rapidfire_state['click_delay']
                    )

                cleanup_binding()

        def start_binding():
            """Enter binding mode and wait for key input."""
            if not is_binding[0]:
                is_binding[0] = True
                bind_text.value = get_translated_text("press_key", current_language)
                bind_button.bgcolor = ft.colors.with_opacity(0.2, ACCENT_COLOR)
                page.on_keyboard_event = on_key
                page.update()
                bind_button.update()

        return bind_button

    key_bind_button = create_key_bind_container(page)
    
    key_container = ft.Container(
        content=ft.Column([
            ft.Text(
                get_translated_text("activation_key", current_language),  # Direkt den übersetzten Text verwenden
                size=TEXT_SIZE,
                color=TEXT_COLOR,
            ),
            ft.Row([
                ft.Container(
                    content=key_bind_button,
                    padding=ft.padding.all(0),
                    border_radius=4,
                    border=ft.border.all(1, ft.colors.with_opacity(0.1, ACCENT_COLOR)),
                    bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
                    expand=True,
                ),
                reset_key_button,
            ]),
        ], spacing=5),
        padding=ft.padding.only(top=10, left=0),
    )


    # Den Container mit den übersetzten Texten erstellen
    main_container = ft.Container(
        content=ft.Column(
            controls=[
                ft.Container(
                    content=ft.Column([
                        ft.Row(
                            controls=[
                                ft.Icon(
                                    ft.Icons.ELECTRIC_BOLT,
                                    color=ACCENT_COLOR,
                                    size=TITLE_SIZE,
                                ),
                                title_text,
                                ft.Container(expand=True),
                                ft.Container(
                                    content=toggle_switch,
                                    padding=ft.padding.only(top=2),
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.START,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                        ft.Container(
                            ft.Divider(color=ft.Colors.with_opacity(0.1, ACCENT_COLOR), height=1),
                            margin=ft.margin.only(top=3, bottom=2),
                        ),
                    ]),
                    padding=ft.padding.only(left=8, right=8, top=0)
                ),
                ft.Container(height=3),
                ft.Container(
                    content=ft.Column([
                        delay_container,
                        key_container,
                    ], spacing=3),
                    padding=ft.padding.symmetric(horizontal=12),
                    expand=True,
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.START,
            spacing=5,
            expand=False,
        ),
        padding=ft.padding.only(left=4, top=12, bottom=12, right=0),
        margin=widget_margin,
        bgcolor=WIDGET_BG,
        border_radius=4,
        border=ft.border.all(1, BORDER_COLOR),
        width=widget_width,
    )
        
    def get_settings():
        return {
            'enabled': rapidfire_state['enabled'],
            'click_delay': rapidfire_state['click_delay'],
            'key': rapidfire_state['key']
        }

    def load_settings(settings):
        if settings:
            rapidfire_state['enabled'] = settings.get('enabled', False)
            rapidfire_state['click_delay'] = int(settings.get('click_delay', 55))
            rapidfire_state['key'] = settings.get('key', 'X')
            
            toggle_switch.controls[1].right = 0 if rapidfire_state['enabled'] else 20
            toggle_switch.controls[1].bgcolor = ACCENT_COLOR if rapidfire_state['enabled'] else ft.colors.GREY_400
            
            delay_slider.value = rapidfire_state['click_delay']
            delay_value.value = str(rapidfire_state['click_delay'])
            
            # Get the bind text control and update its value
            bind_text = key_bind_button.content.controls[0]
            bind_text.value = f"{rapidfire_state['key']}"
            
            rapidfire.set_rapidfire_settings(
                rapidfire_state['enabled'],
                rapidfire_state['key'],
                rapidfire_state['click_delay']
            )
            
            page.update()

    setattr(main_container, 'get_settings', get_settings)
    setattr(main_container, 'load_settings', load_settings)
    setattr(main_container, 'rapidfire', rapidfire)
    setattr(main_container, 'key_bind_button', key_bind_button)
    setattr(main_container, 'update_translations', update_translations)
    
    main_container.get_settings = get_settings
    return main_container

def create_weapon_widget_1(page: ft.Page, recoil_system: RecoilSystem, current_language="en"):
    widget_width = 730
    widget_margin = ft.margin.only(left=-15, right=-15, top=-12, bottom=-12)
   
    def get_translated_text(key, current_language="en"):
        try:
            base_dir = Path(os.path.expanduser("~")) / MAIN_FOLDER
            lang_path = base_dir / "assets" / "languages" / "LANGUAGES.json"
            
            if not os.path.exists(lang_path):
                return get_default_text(key)
                
            with open(lang_path, 'r', encoding='utf-8') as f:
                languages = json.load(f)
                
            if current_language in languages and "settings" in languages[current_language]:
                if key in languages[current_language]["settings"]["recoil"]:
                    return languages[current_language]["settings"]["recoil"][key]
        except Exception as e:
            print(f"Translation error: {e}")
        return get_default_text(key)

    def get_default_text(key):
        defaults = {
            "title": "Recoil Control System",
            "horizontal": "Horizontal Recoil Value",
            "vertical": "Vertical Recoil Value",
            "smoothing": "Smoothing Delay",
            "fire_key": "Fire Key",
            "coming_soon": "Coming soon: Game-based recoil patterns and profiles",
            "press_key": "Press a mouse button...",
            "reset_tooltip": "Reset to default"
        }
        return defaults.get(key, "")

    primary_state = {
        'enabled': False,
        'horizontal': 0,
        'vertical': 20, 
        'smoothing': 0,
        'fire_key': "Mouse Left",
        'controller_mode': False
    }

    def update_recoil_settings(*args):
        horizontal = horizontal_slider.value
        vertical = vertical_slider.value
        smoothing = smoothing_slider.value
        recoil_system.set_recoil_patterns([(horizontal, vertical, smoothing)], 
                                        recoil_system.secondary_recoil_pattern)
        recoil_system.set_recoil_enabled(primary_state['enabled'])

    def toggle_switch_clicked(e):
        primary_state['enabled'] = not primary_state['enabled']
        toggle_switch.controls[1].right = 0 if primary_state['enabled'] else 22
        toggle_switch.controls[1].bgcolor = ACCENT_COLOR if primary_state['enabled'] else ft.colors.GREY_400
        toggle_switch.update()
        update_recoil_settings()
        
        if hasattr(page, 'update_status_overlay'):
            page.update_status_overlay()

    def controller_switch_clicked(e):
        primary_state['controller_mode'] = not primary_state['controller_mode']
        controller_switch.controls[1].right = 0 if primary_state['controller_mode'] else 20
        controller_switch.controls[1].bgcolor = ACCENT_COLOR if primary_state['controller_mode'] else ft.colors.GREY_400
        controller_switch.update()
        recoil_system.set_controller_mode(primary_state['controller_mode'])

    toggle_switch = ft.Stack(
        controls=[
            ft.Container(
                width=45,
                height=18,
                border_radius=12,
                bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
                animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
            ),
            ft.Container(
                width=18,
                height=18,
                right=22,
                bgcolor=ft.colors.GREY_400,
                border_radius=12,
                animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                on_click=lambda e: toggle_switch_clicked(e),
            )
        ]
    )

    controller_switch = ft.Stack(
        controls=[
            ft.Container(
                width=42,
                height=16,
                border_radius=10,
                border=ft.border.all(1, WIDGET_BG),
                animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
            ),
            ft.Container(
                width=16,
                height=16,
                right=22,
                bgcolor=ft.colors.GREY_400,
                border_radius=10,
                animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                on_click=lambda e: controller_switch_clicked(e),
            )
        ]
    )
    def create_labeled_slider(label, min_val=0, max_val=50, default=50, state_key=None):
        display_value = primary_state[state_key] if state_key else default
        
        value_display = ft.TextField(
            value=str(display_value),
            width=42,
            height=22,
            text_size=TEXT_SIZE,
            color=ACCENT_COLOR,
            border_color=ft.colors.TRANSPARENT,
            focused_border_color=ACCENT_COLOR,
            cursor_color=ACCENT_COLOR,
            content_padding=ft.padding.all(4),
            text_align=ft.TextAlign.CENTER,
            keyboard_type=ft.KeyboardType.NUMBER,
            border_radius=4,
            bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
        )

        def on_change_text(e):
            new_text = ''.join(filter(str.isdigit, e.data))
            if new_text != e.data:
                value_display.value = new_text
                value_display.update()
        
        def validate_and_update_value(new_value):
            if new_value == "":
                new_value = "0"
            
            num_value = int(new_value)
            if num_value < min_val:
                num_value = min_val
            elif num_value > max_val:
                num_value = max_val
            
            if state_key:
                primary_state[state_key] = num_value
            slider.value = num_value
            value_display.value = str(num_value)
            update_recoil_settings()
            value_display.update()
            slider.update()

        def on_submit(e):
            validate_and_update_value(e.control.value)

        def reset_value(e):
            if state_key:
                primary_state[state_key] = default
            slider.value = default
            value_display.value = str(default)
            update_recoil_settings()
            
            reset_button.content.color = ACCENT_COLOR
            reset_button.update()
            
            def color_reset_thread():
                time.sleep(0.5)
                reset_button.content.color = ft.colors.YELLOW
                reset_button.update()
            
            threading.Thread(target=color_reset_thread).start()
            
            value_display.update()
            slider.update()

        reset_button = ft.Container(
            content=ft.Icon(
                ft.icons.RESTART_ALT_ROUNDED,
                color=ft.colors.YELLOW,
                size=20,
            ),
            width=24,
            height=24,
            border_radius=4,
            on_click=reset_value,
            tooltip=get_translated_text("reset_tooltip", current_language),
            ink=False,
        )

        value_display.on_change = on_change_text
        value_display.on_submit = on_submit

        slider = ft.Slider(
            min=min_val,
            max=max_val,
            value=display_value,
            active_color=ACCENT_COLOR,
            inactive_color=ft.colors.with_opacity(0.1, ACCENT_COLOR),
            on_change=lambda e: validate_and_update_value(str(int(e.control.value))),
            expand=True,
            height=12,
            width=420,
            divisions=40,
            thumb_color=ACCENT_COLOR,
            adaptive=True,
            round=0,
        )

        container = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(
                        label,
                        size=TEXT_SIZE,
                        color=TEXT_COLOR,
                    ),
                    ft.Container(expand=True),
                    value_display,
                ]),
                ft.Container(height=5),
                ft.Row([
                    ft.Container(
                        content=slider,
                        padding=ft.padding.only(left=-20, right=15),
                        expand=True,
                    ),
                    reset_button,
                ]),
            ], spacing=0),
            padding=ft.padding.only(left=0, top=2, bottom=2),
        )
        
        return container, slider, value_display

    horizontal_container, horizontal_slider, horizontal_value = create_labeled_slider(
        get_translated_text("horizontal", current_language),
        min_val=-50,
        max_val=50,
        default=0,
        state_key='horizontal'
    )

    vertical_container, vertical_slider, vertical_value = create_labeled_slider(
        get_translated_text("vertical", current_language),
        min_val=-50,
        max_val=50,
        default=20,
        state_key='vertical'
    )

    smoothing_container, smoothing_slider, smoothing_value = create_labeled_slider(
        get_translated_text("smoothing", current_language),
        min_val=0,
        max_val=10,
        default=0,
        state_key='smoothing'
    )

    def reset_fire_key(e):
        default_value = "Mouse Left"
        primary_state['fire_key'] = default_value
        key_bind_button.content.controls[0].value = f"{default_value}"
        recoil_system.set_fire_key(default_value)
        
        reset_fire_key_button.content.color = ACCENT_COLOR
        reset_fire_key_button.update()
        
        def color_reset_thread():
            time.sleep(0.5)
            reset_fire_key_button.content.color = ft.colors.YELLOW
            reset_fire_key_button.update()
        
        threading.Thread(target=color_reset_thread).start()
        
        key_bind_button.update()

    reset_fire_key_button = ft.Container(
        content=ft.Icon(
            ft.icons.RESTART_ALT_ROUNDED,
            color=ft.colors.YELLOW,
            size=20,
        ),
        width=24,
        height=24,
        border_radius=4,
        on_click=reset_fire_key,
        tooltip=get_translated_text("reset_tooltip", current_language),
        ink=False,
    )

    def create_key_bind_container(page):
        is_binding = [False]
        
        bind_text = ft.Text(
            f"{primary_state['fire_key']}", 
            size=TEXT_SIZE,
            color=ACCENT_COLOR,
            text_align=ft.TextAlign.CENTER,
        )

        bind_button = ft.Container(
            content=ft.Row(
                [bind_text],
                alignment=ft.MainAxisAlignment.CENTER,
            ),
            width=330,
            height=30,
            border_radius=4,
            bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
            border=ft.border.all(1, ft.colors.with_opacity(1, WIDGET_BG)),
            on_click=lambda e: start_binding(),
            padding=ft.padding.only(top=0),
        )

        def cleanup_binding():
            is_binding[0] = False
            bind_button.bgcolor = ft.colors.with_opacity(0.1, ACCENT_COLOR)
            page.update()
            bind_button.update()

        def on_mouse_click(x, y, button, pressed):
            if is_binding[0] and pressed:
                if button == mouse.Button.left:
                    key_pressed = "Mouse Left"
                elif button == mouse.Button.right:
                    key_pressed = "Mouse Right"
                else:
                    return

                primary_state['fire_key'] = key_pressed
                bind_text.value = f"{key_pressed}"
                recoil_system.set_fire_key(key_pressed)
                cleanup_binding()
                bind_button.update()
                return False

        def start_binding():
            if not is_binding[0]:
                is_binding[0] = True
                bind_text.value = get_translated_text("press_key", current_language)
                bind_button.bgcolor = ft.colors.with_opacity(0.2, ACCENT_COLOR)
                page.update()
                bind_button.update()
                mouse_listener = mouse.Listener(on_click=on_mouse_click)
                mouse_listener.start()

        return bind_button

    key_bind_button = create_key_bind_container(page)
    
    key_container = ft.Container(
        content=ft.Column([
            ft.Text(
                get_translated_text("fire_key", current_language),
                size=TEXT_SIZE,
                color=TEXT_COLOR,
            ),
            ft.Row([
                ft.Container(
                    content=key_bind_button,
                    padding=ft.padding.all(0),
                    border_radius=4,
                    border=ft.border.all(1, ft.colors.with_opacity(1, WIDGET_BG)),
                    bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
                    expand=True,
                ),
                reset_fire_key_button,
            ]),
        ], spacing=5),
        padding=ft.padding.only(top=10, left=0),
    )

    def update_translations(new_language):
        # Update title
        title_text = main_container.content.controls[0].content.controls[0].controls[1]
        title_text.value = get_translated_text("title", new_language)

        # Update horizontal slider label
        horizontal_label = horizontal_container.content.controls[0].controls[0]
        horizontal_label.value = get_translated_text("horizontal", new_language)

        # Update vertical slider label
        vertical_label = vertical_container.content.controls[0].controls[0]
        vertical_label.value = get_translated_text("vertical", new_language)

        # Update smoothing slider label
        smoothing_label = smoothing_container.content.controls[0].controls[0]
        smoothing_label.value = get_translated_text("smoothing", new_language)

        # Update fire key label
        fire_key_label = key_container.content.controls[0]
        fire_key_label.value = get_translated_text("fire_key", new_language)

        # Update coming soon text
        coming_soon_text = main_container.content.controls[2].content.controls[4].content
        coming_soon_text.value = get_translated_text("coming_soon", new_language)

        # Update reset tooltips
        reset_fire_key_button.tooltip = get_translated_text("reset_tooltip", new_language)

        # Update key bind button text if in binding mode
        bind_text = key_bind_button.content.controls[0]
        if bind_text.value == "Press a mouse button...":
            bind_text.value = get_translated_text("press_key", new_language)

        main_container.update()

    main_container = ft.Container(
        content=ft.Column(
            controls=[
                ft.Container(
                    content=ft.Column([
                        ft.Row(
                            controls=[
                                ft.Icon(
                                    ft.Icons.ARCHITECTURE,
                                    color=ACCENT_COLOR,
                                    size=TITLE_SIZE,
                                ),
                                ft.Text(
                                    get_translated_text("title", current_language),
                                    size=TITLE_SIZE,
                                    weight=ft.FontWeight.BOLD,
                                    color=ACCENT_COLOR,
                                ),
                                ft.Container(expand=True),
                                ft.Container(
                                    content=toggle_switch,
                                    padding=ft.padding.only(top=2),
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.START,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                        ft.Container(
                            ft.Divider(color=ft.Colors.with_opacity(0.1, ACCENT_COLOR), height=1),
                            margin=ft.margin.only(top=3, bottom=2),
                        ),
                    ]),
                    padding=ft.padding.only(left=8, right=8, top=0)
                ),
                ft.Container(height=3),
                ft.Container(
                    content=ft.Column([
                        horizontal_container,
                        vertical_container,
                        smoothing_container,
                        key_container,
                        ft.Container(
                            content=ft.Text(
                                get_translated_text("coming_soon", current_language),
                                size=TEXT_SIZE,
                                color=WARN_COLOR,
                                italic=True
                            ),
                            margin=ft.margin.only(top=120),
                        ),
                    ], spacing=3),
                    padding=ft.padding.symmetric(horizontal=12),
                    expand=True,
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.START,
            spacing=5,
            expand=False,
        ),
        padding=ft.padding.only(left=4, top=12, bottom=12, right=0),
        margin=widget_margin,
        bgcolor=WIDGET_BG,
        border_radius=4,
        border=ft.border.all(1, BORDER_COLOR),
        width=widget_width,
    )

    def get_settings():
        return {
            'enabled': primary_state['enabled'],
            'horizontal': primary_state['horizontal'],
            'vertical': primary_state['vertical'],
            'smoothing': primary_state['smoothing'],
            'fire_key': primary_state['fire_key'],
            'controller_mode': primary_state['controller_mode']
        }

    def load_settings(settings):
        if settings:
            primary_state['enabled'] = settings.get('enabled', False)
            primary_state['horizontal'] = int(settings.get('horizontal', 0))
            primary_state['vertical'] = int(settings.get('vertical', 20))
            primary_state['smoothing'] = int(settings.get('smoothing', 0))
            primary_state['fire_key'] = settings.get('fire_key', 'Mouse Left')
            primary_state['controller_mode'] = settings.get('controller_mode', False)
           
            toggle_switch.controls[1].right = 0 if primary_state['enabled'] else 22
            toggle_switch.controls[1].bgcolor = ACCENT_COLOR if primary_state['enabled'] else ft.colors.GREY_400
           
            controller_switch.controls[1].right = 0 if primary_state['controller_mode'] else 22
            controller_switch.controls[1].bgcolor = ACCENT_COLOR if primary_state['controller_mode'] else ft.colors.GREY_400
           
            horizontal_slider.value = primary_state['horizontal']
            horizontal_value.value = str(primary_state['horizontal'])
           
            vertical_slider.value = primary_state['vertical']
            vertical_value.value = str(primary_state['vertical'])
           
            smoothing_slider.value = primary_state['smoothing']
            smoothing_value.value = str(primary_state['smoothing'])
           
            key_bind_button.content.controls[0].value = f"{primary_state['fire_key']}"
           
            recoil_system.set_controller_mode(primary_state['controller_mode'])
            recoil_system.set_fire_key(primary_state['fire_key'])
            update_recoil_settings()
           
            page.update()

    setattr(main_container, 'get_settings', get_settings)
    setattr(main_container, 'load_settings', load_settings)
    setattr(main_container, 'recoil_system', recoil_system)
    setattr(main_container, 'update_translations', update_translations)

    return main_container

def create_weapon_widgets_layout(page: ft.Page, current_language="en"):
    recoil_system = RecoilSystem()  # Entferne .start()
    rapid_fire = RapidFire()        # Entferne .start()

    widget1 = create_weapon_widget_1(page, recoil_system, current_language)
    widget2 = create_rapidfire_widget(page, current_language)
    
    page.primary_recoil = widget1
    page.rapidfire_widget = widget2

    return ft.Container(
        content=ft.Stack(
            controls=[
                widget1,
                widget2,
            ],
        ),
    )