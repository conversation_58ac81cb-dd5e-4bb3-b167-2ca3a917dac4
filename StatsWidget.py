import flet as ft
from DefaultSettings import *
import psutil
import configparser
import time
import json
import os
from pathlib import Path
from datetime import datetime

def create_stat_widget(icon, title, value, accent_color=ACCENT_COLOR):
    return ft.Container(
        content=ft.Column(
            controls=[
                ft.Container(
                    content=ft.Row(
                        controls=[
                            ft.Icon(
                                icon,
                                size=16,
                                color=accent_color
                            ),
                            ft.Text(
                                title,
                                size=13,
                                color=TEXT_COLOR,
                                weight=ft.FontWeight.NORMAL
                            )
                        ],
                        spacing=4,
                    ),
                    padding=ft.padding.only(bottom=5)
                ),
                ft.Text(
                    value,
                    size=16,
                    color=accent_color,
                    weight=ft.FontWeight.BOLD
                )
            ],
            spacing=0,
            alignment=ft.MainAxisAlignment.START,
        ),
        width=165,  # Reduziert von 170
        height=65,  # Reduziert von 65
        bgcolor=WIDGET_BG,
        border=ft.border.all(1, BORDER_COLOR),
        border_radius=4,
        padding=8,
    )

def create_patches_history():
    try:
        patches_path = Path(os.path.expanduser("~")) / MAIN_FOLDER / "assets" / "patches"
        if not os.path.exists(patches_path):
            return ft.Container()

        def get_translation(key):
            return "Patch History" if key == "patch_history" else ""

        def create_patch_panel(date, published, changes):
            content_column = ft.Column(
                controls=[
                    ft.Container(
                        content=ft.Text(
                            change,
                            color=TEXT_COLOR,
                            size=TEXT_SIZE,
                        ),
                        padding=ft.padding.only(left=10)
                    ) for change in changes
                ],
                visible=False
            )

            def toggle_expansion(e):
                content_column.visible = not content_column.visible
                arrow_button.icon = ft.icons.KEYBOARD_ARROW_UP if content_column.visible else ft.icons.KEYBOARD_ARROW_DOWN
                arrow_button.update()
                content_column.update()

            arrow_button = ft.IconButton(
                icon=ft.icons.KEYBOARD_ARROW_DOWN,
                on_click=toggle_expansion,
                icon_color=TEXT_COLOR
            )

            header = ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Icon(ft.icons.UPDATE, color=ACCENT_COLOR, size=TITLE_SIZE),
                        ft.Column(
                            controls=[
                                ft.Text(
                                    f"{date}",
                                    color=TEXT_COLOR,
                                    size=TEXT_SIZE,
                                    weight=ft.FontWeight.BOLD
                                ),
                                ft.Text(
                                    published,
                                    color=TEXT_COLOR,
                                    size=TEXT_SIZE,
                                    italic=True
                                )
                            ],
                            spacing=2
                        ),
                        ft.Container(expand=True),
                        arrow_button
                    ],
                    alignment=ft.MainAxisAlignment.START
                ),
                on_click=toggle_expansion
            )

            return ft.Container(
                content=ft.Column([
                    header,
                    content_column
                ]),
                border=ft.border.all(1, BORDER_COLOR),
                border_radius=4,
                bgcolor=WIDGET_BG,
                padding=8
            )

        patch_widgets = []
        
        for filename in sorted(os.listdir(patches_path), reverse=True):
            if filename.endswith('.ini'):
                config = configparser.ConfigParser()
                config.read(os.path.join(patches_path, filename))
                
                date = config.get('Info', 'date', fallback="Unknown")
                published = config.get('Info', 'published', fallback="")
                changes = [item[1] for item in config.items('Changes')]
                
                patch_panel = create_patch_panel(date, published, changes)
                patch_widgets.append(patch_panel)

        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        get_translation("patch_history"),
                        size=TITLE_SIZE,
                        color=TEXT_COLOR,
                        weight=ft.FontWeight.BOLD
                    ),
                    ft.Container(
                        content=ft.Column(
                            controls=patch_widgets,
                            spacing=5
                        ),
                        padding=ft.padding.only(top=10)
                    )
                ]
            ),
            margin=ft.margin.only(top=10)
        )
    except Exception as e:
        print(f"Error loading patches: {e}")
        return ft.Container()

def create_main_layout():
    current_language = "en"
    
    def get_translation(key):
        return get_default_text(key)
    
    def get_default_text(key):
        defaults = {
            "global_stats": "Global Stats",
            "development_time": "Development Time",
            "subscriptions": "Subscriptions",
            "global_playtime": "Global Playtime",
            "supported_games": "Supported Games",
            "last_detection": "Last Detection",
            "ban_risk": "Ban Risk",
            "security_score": "Security Score",
            "protection_status": "Protection Status",
            "version_info": "Version Information",
            "current_version": "Current Version",
            "last_update": "Last Update",
            "graphics_api": "Graphics API",
            "gpu_support": "GPU Support",
            "patch_history": "Patch History",
            "never": "Never",
            "active": "Active",
            "hours": "Hours",
            "value_hours": "250+ Hours",
            "value_playtime": "50,000+ Hours"
        }
        return defaults.get(key, "")

    def create_stat_section():
        return [
            ft.Container(
                content=ft.Text(
                    get_translation("global_stats"),
                    size=TITLE_SIZE,
                    color=TEXT_COLOR,
                    weight=ft.FontWeight.BOLD
                ),
                padding=ft.padding.only(bottom=8)
            ),
            ft.Container(
                content=ft.Row(
                    controls=[
                        create_stat_widget(
                            ft.icons.SCHEDULE,
                            get_translation("development_time"),
                            get_translation("value_hours")
                        ),
                        create_stat_widget(
                            ft.icons.WORKSPACE_PREMIUM,
                            get_translation("subscriptions"),
                            "2,000+"
                        ),
                        create_stat_widget(
                            ft.icons.TIMELAPSE,
                            get_translation("global_playtime"),
                            get_translation("value_playtime")
                        ),
                        create_stat_widget(
                            ft.icons.SPORTS_ESPORTS,
                            get_translation("supported_games"),
                            "18"
                        ),
                    ],
                    spacing=8,
                    alignment=ft.MainAxisAlignment.START
                ),
            ),
            ft.Container(
                content=ft.Row(
                    controls=[
                        create_stat_widget(
                            ft.icons.SECURITY_UPDATE_GOOD,
                            get_translation("last_detection"),
                            get_translation("never")
                        ),
                        create_stat_widget(
                            ft.icons.GAVEL,
                            get_translation("ban_risk"),
                            "0%"
                        ),
                        create_stat_widget(
                            ft.icons.VERIFIED_USER,
                            get_translation("security_score"),
                            "A+"
                        ),
                        create_stat_widget(
                            ft.icons.SHIELD,
                            get_translation("protection_status"),
                            get_translation("active")
                        ),
                    ],
                    spacing=8,
                    alignment=ft.MainAxisAlignment.START
                ),
                margin=ft.margin.only(top=8)
            ),
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text(
                            get_translation("version_info"),
                            size=TITLE_SIZE,
                            color=TEXT_COLOR,
                            weight=ft.FontWeight.BOLD
                        ),
                        ft.Row(
                            controls=[
                                create_stat_widget(
                                    ft.icons.NEW_RELEASES,
                                    get_translation("current_version"),
                                    VERSION,
                                ),
                                create_stat_widget(
                                    ft.icons.UPDATE,
                                    get_translation("last_update"),
                                    LAST_UPDATE,
                                ),
                                create_stat_widget(
                                    ft.icons.PRECISION_MANUFACTURING_OUTLINED,
                                    get_translation("graphics_api"),
                                    "D3D11/D3D12",
                                ),
                                create_stat_widget(
                                    ft.icons.MEMORY_OUTLINED,
                                    get_translation("gpu_support"),
                                    "DX11+ Cards",
                                ),
                            ],
                            spacing=8,
                        )
                    ]
                ),
                margin=ft.margin.only(top=10)
            ),
            create_patches_history(current_language)
        ]

    layout = ft.Container(
        content=ft.Column(
            controls=create_stat_section(),
            horizontal_alignment=ft.CrossAxisAlignment.START,
            alignment=ft.MainAxisAlignment.START,
            scroll=ft.ScrollMode.AUTO
        ),
        padding=12,
    )

    def update_language(language_key):
        nonlocal current_language
        current_language = language_key
        try:
            # Erstelle neue Controls
            new_controls = create_stat_section()
            
            # Update existierende Controls
            if layout.page:  # Prüfe ob das Layout einer Page zugeordnet ist
                layout.content.controls = new_controls
                layout.update()
            else:
                # Wenn keine Page zugeordnet ist, aktualisiere nur die Controls
                layout.content.controls = new_controls
                
        except Exception as e:
            print(f"Error updating stats language: {e}")
            import traceback
            traceback.print_exc()

    setattr(layout, 'is_stats_widget', True)
    setattr(layout, 'update_language', update_language)
    return layout