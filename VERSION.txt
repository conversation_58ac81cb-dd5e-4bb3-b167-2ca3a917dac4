VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'PERSA'),
            StringStruct(u'FileDescription', u'Vanguard | Advanced Gaming Software'),
            StringStruct(u'FileVersion', u'Orion 1.0.0'),
            StringStruct(u'InternalName', u'Vanguard'),
            StringStruct(u'LegalCopyright', u'PERSA © 2025 - All rights reserved'),
            StringStruct(u'OriginalFilename', u'Vanguard.exe'),
            StringStruct(u'ProductName', u'VANGUARD'),
            StringStruct(u'ProductVersion', u'February 2, 2025 CET'),
            StringStruct(u'Comments', u'Built with latest technologies for optimal gaming performance'),
            StringStruct(u'LegalTrademarks', u'Vanguard is a product of PERSA'),
          ])
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)