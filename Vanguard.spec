# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\AccWidget.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\Auth.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\AutomationWidget.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\ConfigManager.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\Crosshair.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\DefaultSettings.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\GameChecker.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\GamesWidget.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\keyauth.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\MiscWidget.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\rapidfire.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\RecoilWidget.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\StatsWidget.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\updater.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\visuals.py', '.'), ('C:\\Users\\<USER>\\Desktop\\VANGUARD - DEV\\WindowManager.py', '.')],
    hiddenimports=['flet', 'pystray', 'psutil', 'win32gui', 'win32api', 'win32con', 'keyboard', 'keyauth', 'hashlib', 'uuid', 'ctypeskeyauth', 'hashlib', 'uuid', 'ctypes', 'json', 'datetime', 'socket', 'urllib.request', 're'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Vanguard',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='VERSION.txt',
    icon=['Vanguard.ico'],
    manifest='manifest.xml',
)
