import win32gui
import win32con
from ctypes import windll, byref, c_int, sizeof

class WindowManager:
    def __init__(self):
        self.hwnd = None
        self.stream_proof_enabled = True
        
    def set_main_window(self, hwnd):
        self.hwnd = hwnd
        if self.stream_proof_enabled:
            self.set_stream_proof(True)

    def set_stream_proof(self, enable=True):
        if not self.hwnd:
            return
        try:
            if enable:
                windll.user32.SetWindowDisplayAffinity(self.hwnd, 0x00000011)
            else:
                windll.user32.SetWindowDisplayAffinity(self.hwnd, 0)
            self.stream_proof_enabled = enable
        except Exception as e:
            print(f"Failed to set stream proof: {e}")
