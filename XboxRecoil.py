from inputs import get_gamepad
import math
import threading
import time
import ctypes
from ctypes import windll


class XboxController:
    def __init__(self):
        # Controller Status
        self.buttons = {'A': 0, 'B': 0, 'X': 0, 'Y': 0,
                       'LB': 0, 'RB': 0, 'START': 0, 'SELECT': 0}
        self.triggers = {'RT': 0.0, 'LT': 0.0}
        self.sticks = {'LSX': 0.0, 'LSY': 0.0, 'RSX': 0.0, 'RSY': 0.0}
        
        # Recoil System
        self.running = True
        self.recoil_enabled = True
        self.fire_trigger = 'LT'
        
        # Recoil Settings
        self.horizontal_recoil = 0    
        self.vertical_recoil = 2      # Höherer Startwert für vertikalen Recoil
        self.smoothing = 0.001
        
        # Controller-spezifische Parameter
        self.min_trigger_threshold = 0.01  # Minimaler Trigger-Druck
        self.max_intensity = 0.8          # Maximale Intensität
        self.recoil_scale = 8.0         # Reduzierter Skalierungsfaktor
        
        # Performance
        self.user32 = windll.user32
        self.last_trigger = 0.0
        self.lock = threading.Lock()
        
        # Threads
        self._monitor_thread = threading.Thread(target=self._monitor_controller, daemon=True)
        self._recoil_thread = threading.Thread(target=self._apply_recoil, daemon=True)
        self._monitor_thread.start()
        self._recoil_thread.start()

    def set_recoil_values(self, horizontal, vertical, smoothing):
        with self.lock:
            self.horizontal_recoil = horizontal / self.recoil_scale
            self.vertical_recoil = vertical / self.recoil_scale
            self.smoothing = max(0.001, smoothing / 1000.0)

    def move_mouse_relative(self, dx, dy):
        if dx != 0 or dy != 0:
            self.user32.mouse_event(0x0001, int(dx), int(dy), 0, 0)

    def _monitor_controller(self):
        while self.running:
            try:
                events = get_gamepad()
                for event in events:
                    with self.lock:
                        if event.ev_type == 'Absolute':
                            if event.code == 'ABS_Z': 
                                self.triggers['LT'] = event.state / 255.0
                            elif event.code == 'ABS_RZ': 
                                self.triggers['RT'] = event.state / 255.0
                                self.last_trigger = time.time()

            except Exception:
                time.sleep(0.001)

    def _apply_recoil(self):
        last_recoil_time = 0
        accumulated_x = 0.0
        accumulated_y = 0.0
        
        while self.running:
            try:
                current_time = time.time()
                
                with self.lock:
                    trigger_value = self.triggers[self.fire_trigger]
                    h_recoil = self.horizontal_recoil
                    v_recoil = self.vertical_recoil
                    current_smoothing = self.smoothing
                
                if self.recoil_enabled and trigger_value > self.min_trigger_threshold:
                    if current_time - last_recoil_time >= current_smoothing:
                        # Controller-optimierte Intensitätsberechnung
                        intensity = min(trigger_value, self.max_intensity)
                        
                        # Bewegungsberechnung für Controller
                        x_move = h_recoil * intensity * 2
                        y_move = v_recoil * intensity * 2
                        
                        # Akkumuliere kleine Bewegungen
                        accumulated_x += x_move
                        accumulated_y += y_move
                        
                        # Führe Bewegung nur aus, wenn genug akkumuliert wurde
                        if abs(accumulated_x) >= 1 or abs(accumulated_y) >= 1:
                            move_x = int(accumulated_x)
                            move_y = int(accumulated_y)
                            
                            self.move_mouse_relative(move_x, move_y)
                            
                            # Behalte den Rest für die nächste Bewegung
                            accumulated_x -= move_x
                            accumulated_y -= move_y
                        
                        last_recoil_time = current_time

            except Exception:
                pass
            
            time.sleep(0.0001)

    def stop(self):
        self.running = False
        self._monitor_thread.join()
        self._recoil_thread.join()