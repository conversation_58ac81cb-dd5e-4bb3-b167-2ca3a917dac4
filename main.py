# -*- coding: utf-8 -*-
import os
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'
import pystray
import flet as ft
from DefaultSettings import *
import time 
import asyncio
import psutil
import configparser
import threading
import json
from datetime import datetime
import win32gui
from ctypes import windll
import win32gui
import win32api
import win32con
from pathlib import Path
import sys
import keyboard
from PIL import Image
import pygame.mixer

from Auth import main as auth_main
from MouseRecoil import RecoilSystem
from XboxRecoil import XboxController
from RecoilWidget import create_rapidfire_widget, create_weapon_widget_1
from StatsWidget import create_main_layout
from GamesWidget import create_game_card
from AccWidget import create_account_status_widget, create_account_details_widget

from visuals import create_visual_layout
from MiscWidget import create_misc_layout
from ConfigManager import ConfigManager
from WindowManager import WindowManager

from rapidfire import RapidFire
from Crosshair import Crosshair

config_manager = ConfigManager()
window_manager = WindowManager()
pygame.mixer.init()

class AuthState:
    authenticated = False

                  
class LaunchManager:
    def __init__(self, page):
        self.page = page
        self.menu_launched = False
        self.is_paused = False 
        self.status = {
            "is_running": False,
            "launch_text": self.get_footer_text("press_launch"),
            "timer_text": self.get_footer_text("ready"),
            "loading_visible": False,
            "icon_color": ACCENT_COLOR
        }
        self.current_footer = None
        self.timer_running = False
        self.detection_running = False
        
    def get_footer_text(self, key, current_language="en"):
        return self.get_default_text(key)
    
    def get_default_text(self, key):
        defaults = {
            "press_launch": "Press to Launch!",
            "ready": "Ready!",
            "preparing": "Preparing...",
            "not_found": "Game files not found!",
            "start_game": "Please start the game...",
            "config_error": "Could not create target configuration!",
            "game_detected": "Game detected!",
            "press_continue": "Press F1 to continue...",
            "timeout": "Timeout - No game detected",
            "running": "Running:",
            "paused": "Paused:",
            "running_pause": "Running: {game}",
            "not_working": "is not working",
            "press_f12": "Press F12 to continue..."
        }
        return defaults.get(key, "")
        
    def update_footer_controls(self, footer):
        self.current_footer = footer
        if footer and hasattr(footer, 'page') and footer.page is not None:
            launch_icon = footer.controls[1].content.controls[0].content.controls[0]
            launch_text = footer.controls[1].content.controls[0].content.controls[1]
            loading_bar = footer.controls[2].content
            timer_text = footer.controls[1].content.controls[1].content
            
            launch_icon.color = self.status["icon_color"]
            launch_text.color = self.status["icon_color"]
            launch_text.value = self.status["launch_text"]
            loading_bar.visible = self.status["loading_visible"]
            timer_text.value = self.status["timer_text"]

            if footer.page:
                footer.update()
                
    def update_status(self, **kwargs):
        self.status.update(kwargs)
        if self.current_footer and hasattr(self.current_footer, 'page') and self.current_footer.page is not None:
            self.update_footer_controls(self.current_footer)


         
def main(page: ft.Page):
    #if not AuthState.authenticated:
     #   auth_main(page)
       # return
    
    page.window.visible = True 
    page.window.resizable = False  
    page.window.width = 850
    page.window.height = 600
    page.window.min_width = 850
    page.window.min_height = 600
    page.window.max_width = 850
    page.window.max_height = 600
    page.window.frameless = True
    page.window.maximizable = False
    page.window.focused = True
    page.window.prevent_close = False
    f2_handler = None
    
    base_dir = Path(os.path.expanduser("~")) / MAIN_FOLDER 
    ui_dir = base_dir / "assets" / "ui"
    icon_path = str(ui_dir / ICON)

    def set_window_icon_and_style():
        time.sleep(0.1)
        hwnd = win32gui.FindWindow(None, NAME)
        if hwnd:
            if os.path.exists(icon_path):
                icon = win32gui.LoadImage(
                    0, icon_path, win32con.IMAGE_ICON,
                    0, 0, win32con.LR_LOADFROMFILE | win32con.LR_DEFAULTSIZE
                )
                win32api.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_BIG, icon)
                win32api.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_SMALL, icon)
            
            style = win32api.GetWindowLong(hwnd, win32con.GWL_STYLE)
            new_style = style & ~win32con.WS_SYSMENU
            win32api.SetWindowLong(hwnd, win32con.GWL_STYLE, new_style)
            ex_style = win32api.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            new_ex_style = ex_style | win32con.WS_EX_TOOLWINDOW
            win32api.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_ex_style)

    threading.Thread(target=set_window_icon_and_style).start()

    def create_tray_icon():
        from PIL import Image
        
        image = Image.open(icon_path)
        
        def quit_window():
            page.window.close()

        
        def toggle_window(_=None):
            if page.window.visible:
                page.window.visible = False
                page.window.always_on_top = False
                
                hwnd = win32gui.FindWindow(None, NAME)
                if hwnd:
                    win32gui.SetWindowPos(
                        hwnd, 
                        win32con.HWND_NOTOPMOST,
                        0, 0, 0, 0, 
                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                    )
            else:
                page.window.visible = True
                page.window.focused = True
                page.window.always_on_top = True
                
                hwnd = win32gui.FindWindow(None, NAME)
                if hwnd:
                    win32gui.SetWindowPos(
                        hwnd, 
                        win32con.HWND_TOPMOST, 
                        0, 0, 0, 0, 
                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                    )
            
            page.update()
        
        keyboard.on_press_key("insert", toggle_window)
        
        menu = (
            pystray.MenuItem("Toggle Window", toggle_window),
            pystray.MenuItem("Exit", quit_window)
        )
        
        icon = pystray.Icon(
            NAME,
            image,
            NAME,
            menu
        )
        
        return icon

    def run_tray():
        icon = create_tray_icon()
        icon.run()

    tray_thread = threading.Thread(target=run_tray, daemon=True)
    tray_thread.start()

    page.title = NAME
    page.horizontal_alignment = ft.CrossAxisAlignment.START
    page.vertical_alignment = ft.MainAxisAlignment.START
    page.padding = 0
    page.spacing = 0
    page.bgcolor = MAIN_BG

    def handle_window_event(e):
        if e.data == "minimize":
            page.window.visible = False
            page.update()
        elif e.data == "resize":
            hwnd = windll.user32.GetActiveWindow()
            window_manager.set_main_window(hwnd)
        elif e.data == "maximize":
            page.window.width = 850
            page.window.height = 600
            page.update()

    page.window.on_event = handle_window_event

    selected_menu_item = ft.Ref[str]()
    selected_menu_item.current = "Games"
    
    launch_manager = LaunchManager(page)

    menu_items = [
        {
            "id": "Games",
            "icon": ft.Icons.GRID_VIEW,
            "text": "Games"
        },
        {
            "id": "Setting",
            "icon": ft.Icons.AUTO_AWESOME,
            "text": "Settings"
        },
        {
            "id": "Overview",
            "icon": ft.Icons.BAR_CHART,
            "text": "Overview"
        },
        {
            "id": "Subscription",
            "icon": ft.Icons.PERSON_OUTLINE,
            "text": "Account"
        }
    ]


                
    def on_window_focus(e):
        if page.window.always_on_top:
            page.window.focused = True
            page.update()
    
    page.window.on_focus = on_window_focus


    def create_game_widgets_custom():
        return ft.Container(
            content=ft.Stack(
                controls=[
                    create_game_card(),
                ],
            ),
        )
        

    def select_menu_item(item):
        selected_menu_item.current = item

        sidebar = content_area.content.controls[1].controls[0]
        for menu_container in sidebar.content.controls:
            icon = menu_container.content.controls[0]
            text = menu_container.content.controls[1]
            if menu_container.on_click.__defaults__[0] == item:
                icon.color = ACCENT_COLOR
                text.color = ACCENT_COLOR
                menu_container.bgcolor = WIDGET_BG
            else:
                icon.color = TEXT_COLOR
                text.color = TEXT_COLOR
                menu_container.bgcolor = None 
        
        if item == "Games":
            content_area.content.controls = [ 
                custom_title_bar,
                ft.Row(
                    controls=[
                        create_sidebar(),
                        ft.Container(
                            content=ft.Column(
                                controls=[
                                    ft.Container(
                                        content=create_game_card(),
                                        expand=True,
                                    ),
                                ],
                                spacing=0,
                            ),
                            expand=True,
                        ),
                    ],
                    expand=True,
                    spacing=0,
                ),
                create_custom_footer()
            ]
                
        elif item == "Setting":
            weapon_tabs = [
                ("Recoil", None),
                ("Rapid & Cross", None),
                ("Automation", None)
            ]
            weapon_tab_containers = []
            weapon_content = None

            def create_weapon_tab(text, icon):
                tab_container = ft.Container(
                    content=ft.Row(
                        [
                            ft.Text( 
                                text, 
                                size=14,
                                color=ACCENT_COLOR if text == "weapon" else TEXT_COLOR,
                                weight=ft.FontWeight.NORMAL,
                            ),
                        ],
                        spacing=5,
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    padding=ft.padding.only(left=20, right=25),
                    data=text,
                    on_click=lambda e, t=text: handle_weapon_tab_click(t)
                )
                weapon_tab_containers.append(tab_container)
                return tab_container

            def handle_weapon_tab_click(tab_name):
                for container in weapon_tab_containers:
                    text = container.content.controls[0] 
                    text.color = ACCENT_COLOR if container.data == tab_name else TEXT_COLOR
                
                if tab_name == "Recoil":
                    if not hasattr(page, "recoil_layout"):
                        if not hasattr(page, "recoil_system"):
                            recoil_system = RecoilSystem()
                            page.recoil_system = recoil_system

                        recoil_layout = create_weapon_widget_1(page, page.recoil_system, "en")
                        page.recoil_layout = recoil_layout
                    else:
                        recoil_layout = page.recoil_layout

                    weapon_content.content = recoil_layout
                    content_area.update()

                elif tab_name == "Rapid & Cross":
                    if not hasattr(page, "rapidcross_layout"):
                        # Nur initialisieren wenn noch nicht vorhanden
                        if not hasattr(page, "rapid_fire"):
                            rapid_fire = RapidFire()
                            page.rapid_fire = rapid_fire

                        rapidfire_widget = create_rapidfire_widget(page, "en")
                        crosshair_layout = create_visual_layout(page, window_manager, "en")
                        
                        rapidcross_layout = ft.Container(
                            content=ft.Stack(
                                controls=[
                                    crosshair_layout,
                                    rapidfire_widget
                                ],
                            ),
                        )
                        
                        page.rapidcross_layout = rapidcross_layout

                        if isinstance(crosshair_layout.content, ft.Stack):
                            page.crosshair_widget = crosshair_layout.content.controls[0]
                    else:
                        rapidcross_layout = page.rapidcross_layout

                    weapon_content.content = rapidcross_layout
                    content_area.update()

                elif tab_name == "Automation":
                    if not hasattr(page, "misc_layout"):
                        misc_layout = create_misc_layout(page, "en")
                        page.misc_layout = misc_layout

                        if isinstance(misc_layout.content, ft.Stack):
                            page.general_widget = misc_layout.content.controls[0]
                            page.config_widget = misc_layout.content.controls[1]

                        weapon_content.content = misc_layout
                        page.update()

                        if hasattr(page.config_widget, 'load_current_config'):
                            page.config_widget.load_current_config()
                    else:
                        misc_layout = page.misc_layout
                        weapon_content.content = misc_layout
                
                content_area.update()

            tab_controls = [create_weapon_tab(text, icon) for text, icon in weapon_tabs]
            
            weapon_menu = ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Container(
                                    content=ft.Row(
                                        tab_controls,
                                        alignment=ft.MainAxisAlignment.CENTER,
                                    ),
                                    expand=True,
                                    alignment=ft.alignment.center,
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                    ],
                    spacing=0,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                padding=ft.padding.only(left=0, right=0, top=8, bottom=8),
                height=36,
                bgcolor=WIDGET_BG,
                margin=ft.margin.only(left=5, right=5, top=5),
                border_radius=4,
                border=ft.border.all(1, BORDER_COLOR),
            )
            weapon_content = ft.Container(
                content=ft.Text(
                    "Content Area",
                    color=TEXT_COLOR
                ),
                expand=True,
                padding=20,
            )

            content_area.content.controls = [
                custom_title_bar,
                ft.Row(
                    controls=[
                        create_sidebar(),
                        ft.Container(
                            content=ft.Column(
                                controls=[
                                    weapon_menu,
                                    weapon_content
                                ],
                                spacing=0,
                                expand=True,
                            ),
                            expand=True,
                        ),
                    ],
                    expand=True,
                    spacing=0,
                ),
                create_custom_footer()
            ]
        
            page.update()
        
            handle_weapon_tab_click("Recoil")
            
        elif item == "Overview":
            stats_layout = create_main_layout()
            
            content_area.content.controls = [
                custom_title_bar,
                ft.Row(
                    controls=[
                        create_sidebar(),
                        ft.Container(
                            content=ft.Column(
                                controls=[
                                    ft.Container(
                                        content=stats_layout,
                                        expand=True,
                                    ),
                                ],
                                spacing=0,
                            ),
                            expand=True,
                        ),
                    ],
                    expand=True,
                    spacing=0,
                ),
                create_custom_footer()
            ]
            
            page.stats_widget = stats_layout
            page.update()  # Update page first
            
            # Dann aktualisiere die Sprache
            if hasattr(stats_layout, 'update_language'):
                stats_layout.update_language("en")
                
            
        elif item == "Subscription":
            account_status = create_account_status_widget("en")
            account_details = create_account_details_widget("en")
            
            content_area.content.controls = [
                custom_title_bar,
                ft.Row(
                    controls=[
                        create_sidebar(),
                        ft.Container(
                            content=ft.Column(
                                controls=[
                                    ft.Container(
                                        content=ft.Column(
                                            controls=[
                                                ft.Container(
                                                    content=account_status,
                                                    margin=ft.margin.only(bottom=20), 
                                                ),
                                                ft.Container(
                                                    content=account_details,
                                                ),
                                            ],
                                            alignment=ft.MainAxisAlignment.CENTER,
                                            spacing=0,
                                        ),
                                        expand=True,
                                        padding=20,
                                    ),
                                ],
                                spacing=0,
                            ),
                            expand=True,
                        ),
                    ],
                    expand=True,
                    spacing=0,
                ),
                create_custom_footer()
            ]
        else:
            content_area.content.controls = [ 
                custom_title_bar,
                ft.Row(
                    controls=[
                        create_sidebar(),
                        ft.Container(
                            content=ft.Column(
                                controls=[
                                    ft.Container(
                                        content=ft.Column(
                                            controls=[
                                                ft.Text(f"Selected menu item: {item}", size=20),
                                                ft.Text("Right side widget 2", size=16),
                                            ],
                                        ),
                                        expand=True,
                                        padding=20,
                                    ),
                                ],
                                spacing=0,
                            ),
                            expand=True,
                        ),
                    ],
                    expand=True,
                    spacing=0,
                ),
                create_custom_footer()
            ]

        content_area.update()
    
    def minimize_window(e):
        page.window.minimized = True
        page.update()
        

    def create_sidebar():
        sidebar_items = [
            ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Icon(
                            menu_items[0]["icon"],
                            color=ACCENT_COLOR if menu_items[0]["id"] == selected_menu_item.current else TEXT_COLOR,
                            size=16,
                        ),
                        ft.Text(
                            menu_items[0]["text"],
                            color=ACCENT_COLOR if menu_items[0]["id"] == selected_menu_item.current else TEXT_COLOR,
                            size=14,
                            weight=ft.FontWeight.NORMAL,
                        )
                    ],
                    spacing=10,
                ),
                padding=ft.padding.only(left=10, top=5, bottom=5, right=25),
                margin=ft.margin.only(top=10),
                on_click=lambda e, itm=menu_items[0]["id"]: select_menu_item(itm),
                bgcolor=WIDGET_BG if menu_items[0]["id"] == selected_menu_item.current else None,
            ),
            *[ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Icon(
                            item["icon"],
                            color=ACCENT_COLOR if item["id"] == selected_menu_item.current else TEXT_COLOR,
                            size=16,
                        ),
                        ft.Text(
                            item["text"],
                            color=ACCENT_COLOR if item["id"] == selected_menu_item.current else TEXT_COLOR,
                            size=14,
                            weight=ft.FontWeight.NORMAL,
                        )
                    ],
                    spacing=10,
                ),
                padding=ft.padding.only(left=10, top=5, bottom=5, right=25),
                on_click=lambda e, itm=item["id"]: select_menu_item(itm),
                bgcolor=WIDGET_BG if item["id"] == selected_menu_item.current else None,
            ) for item in menu_items[1:]]
        ]

        return ft.Container(
            width=130,
            height=None,
            bgcolor=WIDGET_BG,
            border=ft.border.only(right=ft.BorderSide(1, BORDER_COLOR)),
            content=ft.Column(
                controls=sidebar_items,
                spacing=5,
            ),
        )
    
    




        


    custom_title_bar = ft.Column(
        controls=[
            ft.WindowDragArea(
                content=ft.Container(
                    height=40,
                    bgcolor=WIDGET_BG,
                    content=ft.Row(
                        controls=[
                            # Linke Seite mit Logo/Name
                            ft.Container( 
                                content=ft.Row(
                                    controls=[
                                        ft.Image(
                                            src=str(ui_dir / ICON),
                                            width=24,
                                            height=24,
                                        ),
                                        ft.Text(
                                            NAME.upper(),
                                            size=14,
                                            color=ACCENT_COLOR,
                                            weight=ft.FontWeight.NORMAL,
                                        ),
                                    ],
                                    spacing=8,
                                    vertical_alignment=ft.CrossAxisAlignment.CENTER,
                                ),
                                padding=ft.padding.only(left=12, top=0),
                                width=120, 
                            ),
                            ft.Container(
                                expand=True,
                            ),
                            ft.Container(
                                content=ft.Row(
                                    controls=[
                                        ft.Container(
                                            content=ft.Text(
                                                "INSERT",
                                                color=ACCENT_COLOR,
                                                size=13,
                                                weight=ft.FontWeight.NORMAL,
                                            ),
                                            padding=ft.padding.only(top=4, bottom=8, left=0, right=0),
                                        ),
                                        ft.Container(
                                            content=ft.IconButton(
                                                icon=ft.Icons.CLOSE,
                                                icon_color=DETECTED_COLOR,
                                                icon_size=TITLE_SIZE,
                                                on_click=lambda _: page.window.close(),
                                            ),
                                            padding=ft.padding.only(top=2, bottom=4, left=2, right=2),
                                        ),
                                    ],
                                    spacing=10,
                                ),
                                width=100,
                            ),
                        ],
                        spacing=0,
                    ),
                ),
            ),
            ft.Divider(
                color=ft.Colors.with_opacity(1, BORDER_COLOR),
                thickness=1,
                height=1,
            ),
        ],
        spacing=0,
    )

    
    def create_custom_footer():
        loading_bar = ft.ProgressBar(
            width=None,
            height=1,
            color=ACCENT_COLOR,
            bgcolor=WIDGET_BG,
            visible=False,
            expand=True 
        )

        launch_icon = ft.Icon(
            ft.Icons.ROCKET_LAUNCH,
            color=launch_manager.status["icon_color"],
            size=TITLE_SIZE,
        )
        
        launch_text = ft.Text(
            launch_manager.status["launch_text"],
            color=launch_manager.status["icon_color"],
            size=TEXT_SIZE,
            weight=ft.FontWeight.NORMAL,
            font_family="Roboto"
        )

        timer_text = ft.Text(
            launch_manager.status["timer_text"],
            color=ACCENT_COLOR,
            size=TEXT_SIZE,
            weight=ft.FontWeight.NORMAL,
            font_family="Roboto"
        )

        
        def read_exe_ini():
            try:
                # Define the game mappings directly in code
                game_mappings = {
                    "Apex Legends": ["r5apex.exe", "r5apex_dx12.exe"],
                    "Escape from Tarkov": ["EscapeFromTarkov.exe"],
                    "Fortnite": ["FortniteClient-Win64-Shipping.exe", "FortniteClient-Win64-Shipping_EAC_EOS.exe"],
                    "Call of Duty: Modern Warfare": ["ModernWarfare.exe"],
                    "Overwatch 2": ["Overwatch.exe", "OverwatchLauncher.exe"],
                    "PlayerUnknown's Battlegrounds": ["TslGame.exe"],
                    "Rainbow Six Siege": ["RainbowSix.exe", "RainbowSix_BE.exe", "RainbowSix_Vulkan.exe"],
                    "Rust": ["RustClient.exe"],
                    "XDefiant": ["XDefiant.exe", "XDefiant_BE.exe"],
                    "Black Ops 6": ["cod.exe"],
                    "Modern Warfare 2": ["cod22-cod.exe"],
                    "Modern Warfare 3": ["cod23-cod.exe"],
                    "Counter-Strike 2": ["cs2.exe"],
                    "Delta Force": ["DeltaForceClient-Win64-Shipping.exe"]
                }

                # Convert to the format expected by the rest of the code
                exe_dict = {}
                for game_name, exes in game_mappings.items():
                    for exe in exes:
                        if exe in [e for v in exe_dict.values() for e in v]:
                            continue
                        if game_name not in exe_dict:
                            exe_dict[game_name] = []
                        exe_dict[game_name].append(exe)

                return list(exe_dict.keys()), exe_dict

            except Exception as e:
                print(f"Error creating exe dictionary: {e}")
                return None, None

        async def safe_game_check(exe_dict):
            start_time = time.time()
            while time.time() - start_time < 120:
                game_name = get_running_game(exe_dict)
                if game_name:
                    return game_name
                await asyncio.sleep(1)
            return None
        
        def get_running_game(exe_dict):
            if not exe_dict:
                return None
            try:
                for proc in psutil.process_iter(['name']):
                    proc_name = proc.info['name'].lower()
                    for game_name, exes in exe_dict.items():
                        # Use exact matching by comparing full process names
                        if proc_name in [exe.lower() for exe in exes]:
                            return game_name
            except Exception as e:
                print(f"Error checking processes: {e}")
            return None
        
        def create_target_ini(game_name, exe_dict):
            try:
                user_path = os.path.expanduser('~')
                target_path = os.path.join(user_path, MAIN_FOLDER , 'assets', 'games')
                target_ini_path = os.path.join(target_path, 'target.ini')
                
                target_exe = None
                for proc in psutil.process_iter(['name']):
                    proc_name = proc.info['name'].lower()
                    if any(exe.lower() == proc_name for exe in exe_dict[game_name]):
                        target_exe = proc_name
                        break
                
                if target_exe:
                    config = configparser.ConfigParser()
                    config[game_name] = {'target_exe': target_exe}
                    
                    os.makedirs(target_path, exist_ok=True)
                    
                    with open(target_ini_path, 'w') as f:
                        config.write(f)
                    
                    return target_exe
            except Exception as e:
                print(f"Error creating target.ini: {e}")
            return None

        def read_target_exe(game_name):
            try:
                user_path = os.path.expanduser('~')
                target_ini_path = os.path.join(user_path, MAIN_FOLDER , 'assets', 'games', 'target.ini')
                if not os.path.exists(target_ini_path):
                    return None
                
                config = configparser.ConfigParser()
                config.read(target_ini_path)
                
                if game_name in config:
                    return config[game_name].get('target_exe')
                return None
            except:
                return None
        
        def toggle_pause(e):
            print("F2 wurde gedrückt")
            launch_manager.is_paused = not launch_manager.is_paused
            try:
                sound_path = str(Path(os.path.expanduser('~')) / MAIN_FOLDER  / "assets" / "sounds" / "pause.wav")
                sound = pygame.mixer.Sound(sound_path)
                sound.play()
            except Exception as err:
                print(f"Error playing pause sound: {err}")
            
            if hasattr(page, "recoil_system"):
                if launch_manager.is_paused:
                    page.recoil_system.pause()
                else:
                    page.recoil_system.resume()
            
            
            # Greife auf das aktuell gespeicherte Spiel zu:
            current_game = getattr(page, "current_game", "Unknown")
            running_text = launch_manager.get_footer_text("running" if not launch_manager.is_paused else "paused")
            launch_manager.update_status(
                launch_text=f"{running_text} {current_game}"
            )
            
        async def delayed_check(e):
            if not launch_manager.status["is_running"]:
                try:
                    current_time = time.time()
                    if hasattr(e.control, 'last_click_time'):
                        if current_time - e.control.last_click_time < 10:
                            return
                    e.control.last_click_time = current_time

                    launch_manager.update_status(
                        is_running=True,
                        icon_color=ACCENT_COLOR,
                        text_color=WARN_COLOR,
                        launch_text=launch_manager.get_footer_text("preparing"),
                        loading_visible=True
                    )
                    
                    await asyncio.sleep(2)
                    
                    game_names, exe_dict = read_exe_ini()
                    if not game_names:
                        launch_manager.update_status(
                            launch_text="Game files not found!",
                            is_running=False
                        )
                        return

                    await asyncio.sleep(2)
                    
                    launch_manager.update_status(
                        launch_text=launch_manager.get_footer_text("start_game")
                    )
                    
                    launch_manager.detection_running = True
                    for remaining in range(120, -1, -1):
                        if not launch_manager.detection_running:
                            break
                        if remaining > 0:
                            minutes = remaining // 60
                            seconds = remaining % 60
                            launch_manager.update_status(timer_text=f"{minutes:02d}:{seconds:02d}")
                            
                            game_name = get_running_game(exe_dict)
                            if game_name:
                                # Lade die games.ini um den Status zu prüfen
                                config = configparser.ConfigParser(allow_no_value=True)
                                config.optionxform = str  # Behält Groß-/Kleinschreibung bei
                                try:
                                    games_ini_path = Path(os.path.expanduser('~')) / MAIN_FOLDER / 'assets' / 'games' / 'games.ini'
                                    # Lese die Datei als Text
                                    with open(games_ini_path, 'r', encoding='utf-8') as f:
                                        content = '[DEFAULT]\n' + f.read()
                                    # Bereinige problematische Zeichen
                                    content = content.replace('>', '=')
                                    # Parse den bereinigten Inhalt
                                    config.read_string(content)
                                    
                                    if game_name in config:
                                        game_status = config[game_name]['status'].lower()
                                        if game_status != "working":
                                            launch_manager.update_status(
                                                launch_text=f"{game_name} {launch_manager.get_footer_text('not_working')}",
                                                loading_visible=False,
                                                is_running=False,
                                                text_color=DETECTED_COLOR,
                                                icon_color=DETECTED_COLOR,
                                                timer_text=launch_manager.get_footer_text("ready")
                                            )
                                            return
                                except Exception as e:
                                    print(f"Error reading games.ini: {e}")
                                
                                # Erstelle target.ini nach erfolgreicher Statusprüfung
                                target_exe = create_target_ini(game_name, exe_dict)
                                if not target_exe:
                                    launch_manager.update_status(
                                        launch_text=launch_manager.get_footer_text("config_error"),
                                        is_running=False
                                    )
                                    return

                                launch_manager.update_status(
                                    launch_text=f"{game_name} {launch_manager.get_footer_text('game_detected')}",
                                    loading_visible=False
                                )
                                await asyncio.sleep(3)
                                
                                press_f12_text = launch_manager.get_footer_text("press_f12")
                                launch_manager.update_status(launch_text=f"{press_f12_text}")
                                
                                # Speichere das erkannte Spiel global (z.B. auf page)
                                page.current_game = game_name
                                
                                f12_pressed = False
                                while not f12_pressed and launch_manager.detection_running:
                                    if keyboard.is_pressed('f12'):
                                        f12_pressed = True
                                        await asyncio.sleep(2)  # Nutze await statt time.sleep
                                        try:
                                            sound_path = str(Path(os.path.expanduser('~')) / MAIN_FOLDER / "assets" / "sounds" / "load.wav")
                                            sound = pygame.mixer.Sound(sound_path)
                                            sound.play()
                                            
                                            if not hasattr(page, "recoil_system"):
                                                page.recoil_system = RecoilSystem()
                                                
                                            if not hasattr(page, "rapid_fire"):
                                                page.rapid_fire = RapidFire()
                                                
                                            page.recoil_system.start()
                                            page.rapid_fire.start()
                                            
                                            launch_manager.is_paused = False
                                            
                                            # Registriere den F2-Hook einmalig und speichere den Handle
                                            f2_hook = keyboard.on_press_key("f2", toggle_pause)
                                            
                                        except Exception as err:
                                            print(f"Error initializing systems: {err}")
        
                                if not f12_pressed:
                                    return
                                running_text = launch_manager.get_footer_text("running")
                                launch_manager.update_status(launch_text=f"{running_text} {game_name}")

                                start_time = time.time()
                                launch_manager.timer_running = True
                                while launch_manager.timer_running and launch_manager.detection_running:
                                    try:
                                        elapsed_seconds = int(time.time() - start_time)
                                        hours = elapsed_seconds // 3600
                                        minutes = (elapsed_seconds % 3600) // 60
                                        seconds = elapsed_seconds % 60
                                        
                                        # Aktualisiere den Running-Text bei jedem Timer-Update
                                        running_text = launch_manager.get_footer_text("running_pause")
                                        launch_manager.update_status(
                                            timer_text=f"{hours:02d}:{minutes:02d}:{seconds:02d}",
                                            launch_text=running_text.format(game=game_name)  # Verwende format() für den Platzhalter
                                        )
                                        
                                        await asyncio.sleep(1)
                                        
                                        if elapsed_seconds % 5 == 0:
                                            target_running = False
                                            for proc in psutil.process_iter(['name']):
                                                if proc.info['name'].lower() == target_exe.lower():
                                                    target_running = True
                                                    break
                                            if not target_running:
                                                launch_manager.timer_running = False
                                                launch_manager.detection_running = False
                                                launch_manager.update_status(
                                                    is_running=False,
                                                    icon_color=ACCENT_COLOR,
                                                    text_color=ACCENT_COLOR,
                                                    launch_text="Press to Launch!",
                                                    loading_visible=False,
                                                    timer_text="Ready!"
                                                )
                                                return
                                    except:
                                        launch_manager.timer_running = False
                                        launch_manager.detection_running = False
                                        launch_manager.update_status(is_running=False)
                                        launch_manager.update_status(is_running=False)
                                        break
                                
                                return
                            
                            await asyncio.sleep(1)
                        else:
                            launch_manager.detection_running = False
                            launch_manager.update_status(
                                is_running=False,
                                icon_color=ACCENT_COLOR,
                                text_color=ACCENT_COLOR,
                                launch_text="Timeout - No game detected",
                                loading_visible=False,
                                timer_text="Ready!"
                            )
                
                except Exception as e:
                    print(f"Error in delayed_check: {e}")
                    launch_manager.timer_running = False
                    launch_manager.detection_running = False
                    launch_manager.update_status(
                        is_running=False,
                        icon_color=ACCENT_COLOR,
                        text_color=ACCENT_COLOR,
                        launch_text="Press to Launch!",
                        loading_visible=False,
                        timer_text="Ready!"
                    )
            else:
                launch_manager.timer_running = False
                launch_manager.detection_running = False
                launch_manager.update_status(
                    is_running=False,
                    icon_color=ACCENT_COLOR,
                    text_color=ACCENT_COLOR,
                    launch_text="Press to Launch!",
                    loading_visible=False,
                    timer_text="Ready!"
                )

        footer = ft.Column(
            controls=[
                ft.Divider(
                    color=ft.Colors.with_opacity(1, BORDER_COLOR),
                    thickness=1,
                    height=1,
                ),
                ft.Container(
                    height=40,
                    bgcolor=WIDGET_BG,
                    content=ft.Row(
                        controls=[
                            ft.Container(
                                content=ft.Row(
                                    controls=[
                                        launch_icon,
                                        launch_text,
                                    ],
                                    spacing=5,
                                ),
                                padding=ft.padding.only(left=10),
                                on_click=lambda e: asyncio.run(delayed_check(e)),
                            ),
                            ft.Container(
                                content=timer_text,
                                padding=ft.padding.only(right=10),
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        vertical_alignment=ft.CrossAxisAlignment.CENTER,
                    ),
                ),
                ft.Container(
                    height=1,
                    content=loading_bar,
                    padding=0,
                    bgcolor=WIDGET_BG
                ),
            ],
            spacing=0,
        )
        
        launch_manager.update_footer_controls(footer)
        
        return footer
        

    content_area = ft.Container(
        content=ft.Column(
            controls=[
                custom_title_bar,
                ft.Row(
                    controls=[
                        create_sidebar(),
                        ft.Container(
                            content=ft.Column(
                                controls=[
                                    ft.Container(
                                        content=create_game_widgets_custom(),
                                        expand=True,
                                    ),
                                ],
                                spacing=0,
                            ),
                            expand=True,
                        ),
                    ],
                    expand=True,
                    spacing=0,
                ),
                create_custom_footer()
            ],
            spacing=0,
        ),
        padding=0,
    )

    page.add(
        ft.Container(
            content=content_area,
            expand=True,
            padding=0,
        )
    )
if __name__ == "__main__":
    ft.app(target=main)