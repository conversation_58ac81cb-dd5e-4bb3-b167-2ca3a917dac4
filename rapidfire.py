import win32api
import win32con
import threading
import time
import ctypes
from GameChecker import is_supported_game_active, can_perform_action
import win32gui
import win32ui
from ctypes import windll
from PIL import Image, ImageDraw
from pynput.mouse import <PERSON><PERSON>, Controller
from pynput.mouse import <PERSON><PERSON>, Controller, Listener 
from MouseRecoil import RecoilSystem

class RapidFire:
    def __init__(self, recoil_system=None):
        self.running = True
        self.paused = False
        self.rapidfire_enabled = False
        self.lock = threading.Lock()

        self.recoil_system = recoil_system
        self.rapidfire_thread = None

    
    def move_mouse_relative(self, dx, dy):
        ctypes.windll.user32.mouse_event(0x0001, int(dx), int(dy), 0, 0)

    
    def start(self):
        self.rapidfire_thread = threading.Thread(target=self.run_rapidfire)
        self.rapidfire_thread.daemon = True
        self.rapidfire_thread.start()


    def stop(self):
        self.running = False
        if self.rapidfire_thread:
            self.rapidfire_thread.join()

    def set_rapidfire_settings(self, enabled, key, delay):
        with self.lock:
            self.rapidfire_enabled = enabled
            self.rapidfire_key = key
            if delay == 0:
                self.click_delay = 50
            else:
                self.click_delay = delay / 100


    def check_rapidfire_key(self):
        key_map = {
            "X": ord("X"),
            "C": ord("C"),
            "V": ord("V")
        }
        return win32api.GetAsyncKeyState(key_map.get(self.rapidfire_key, 0)) & 0x8000

    def perform_click(self):
        if self.recoil_system and self.recoil_system.recoil_enabled:
            # Sync mit RecoilTab bei jedem Click
            self.sync_with_recoil_tab()
            
            # Hole das aktuelle Pattern
            pattern = (self.recoil_system.secondary_recoil_pattern 
                    if self.recoil_system.use_secondary_recoil 
                    else self.recoil_system.recoil_pattern)
            
            # Mouse down
            ctypes.windll.user32.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            
            # Wende das Pattern an
            for dx, dy, delay in pattern:
                if not self.is_firing:
                    break
                self.move_mouse_relative(dx, dy)
                time.sleep(delay / 1000.0)
            
            # Mouse up
            ctypes.windll.user32.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        else:
            # Normaler Click ohne Recoil
            ctypes.windll.user32.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(0.001)
            ctypes.windll.user32.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)

    def run_rapidfire(self):
        last_click_time = 0

        while self.running:
            current_time = time.time()

            if self.rapidfire_enabled and not self.paused and can_perform_action():
                # Check for pattern changes
                if self.recoil_system:
                    self.check_recoil_pattern()
                    
                if self.check_rapidfire_key():
                    self.is_firing = True

                    if current_time - last_click_time >= max(0.001, self.click_delay):
                        self.perform_click()
                        last_click_time = current_time
                else:
                    self.is_firing = False

            time.sleep(0.001)

    def set_recoil_enabled(self, enabled):
        with self.lock:
            self.recoil_enabled = enabled

    def set_recoil_patterns(self, primary_pattern, secondary_pattern):
        with self.lock:
            self.recoil_pattern = primary_pattern
            self.secondary_recoil_pattern = secondary_pattern


    def is_active(self):
        return self.running and not self.paused

