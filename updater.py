import flet as ft
import threading
import time
import firebase_admin
from firebase_admin import credentials, db, storage
import os
import sys
from pathlib import Path
from DefaultSettings import *

FIREBASE_CONFIG = ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
class UpdateManager:
    def __init__(self):
        try:
            cred = credentials.Certificate(FIREBASE_CONFIG)
            firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://auto-updater-8fda2-default-rtdb.europe-west1.firebasedatabase.app/',
                'storageBucket': 'auto-updater-8fda2.appspot.com'
            })
        except Exception:
            sys.exit(1)
        
        self.base_dir = Path(os.path.expanduser("~")) / MAIN_FOLDER
        self.patches_dir = self.base_dir / "assets" / "patches"
        self.games_dir = self.base_dir / "assets" / "games"
        self.ui_dir = self.base_dir / "assets" / "ui"
        self.sounds_dir = self.base_dir / "assets" / "sounds"
        
        self.create_directories()
        self.bucket = storage.bucket()

    
    def create_directories(self):
        for directory in [self.patches_dir, self.games_dir, self.ui_dir,
                        self.sounds_dir]:
            try:
                directory.mkdir(parents=True, exist_ok=True)
            except Exception:
                pass

    def download_file(self, remote_file, local_path):
        try:
            blob = self.bucket.blob(remote_file)
            if blob.exists():
                blob.download_to_filename(local_path)
                return True
            return False
        except Exception:
            return False

    def update_files(self):
        success = True
        
        if not self.download_file(ICON, self.ui_dir / ICON):
            success = False
            
            

        
        if not self.download_file('load.wav', self.sounds_dir / 'load.wav'):
            success = False
            
        if not self.download_file('click.wav', self.sounds_dir / 'click.wav'):
            success = False
            
        if not self.download_file('pause.wav', self.sounds_dir / 'pause.wav'):
            success = False
            
        game_images = [
            "Apex Legends.jpg", "Battlefiels 2024.jpg", "Black Ops 6.png",
            "Counter-Strike 2.jpg", "DayZ.png", "Delta Force.jpg",
            "Escape from Tarkov.jpg", "Fortnite.jpg", "Modern Wafare 2019.jpg",
            "Modern Wafare II.jpg", "Modern Wafare III.jpg", "Overwatch 2.jpg",
            "PUBG.jpg", "Rainbow Six Siege.png", "Rust.jpg", "The Finals.jpg",
            "Xdefiant.jpg", "placeholder.png"
        ]
        
        for image in game_images:
            if not self.download_file(image, self.ui_dir / image):
                success = False
        
        if not self.download_file('games.ini', self.games_dir / 'games.ini'):
            success = False
        
        blobs = self.bucket.list_blobs()
        patch_files = [blob.name for blob in blobs if blob.name.startswith('patch-') and blob.name.endswith('.ini')]
        
        for patch_file in patch_files:
            if not self.download_file(patch_file, self.patches_dir / patch_file):
                success = False
        
        return success

class FileDownloader:
    def __init__(self, page: ft.Page):
        self.page = page
        self.status_text = ft.Text(
            "Checking assets...", 
            color=TEXT_COLOR, 
            size=13,
            text_align=ft.TextAlign.CENTER,
            weight=ft.FontWeight.W_500
        )
        self.progress_bar = ft.ProgressBar(
            width=350,
            color=ACCENT_COLOR,
            bgcolor=WIDGET_BG,
            height=2
        )
        self.setup_ui()

    def setup_ui(self):
        custom_title_bar = ft.Container(
            height=30,
            bgcolor=WIDGET_BG,
            content=ft.Row(
                controls=[
                    ft.WindowDragArea(
                        content=ft.Row(
                            controls=[
                                ft.Container(
                                    content=ft.Text(
                                        NAME.upper(),
                                        color=ACCENT_COLOR,
                                        size=TEXT_SIZE,
                                        weight=ft.FontWeight.BOLD
                                    ),
                                    padding=ft.padding.only(left=10),
                                ),
                            ],
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                        expand=True
                    ),
                    ft.Container(
                        content=ft.IconButton(
                            icon=ft.Icons.CLOSE,
                            icon_color=TEXT_COLOR,
                            icon_size=TITLE_SIZE,
                            on_click=lambda _: self.page.window.close(),
                            style=ft.ButtonStyle(
                                overlay_color=ft.Colors.TRANSPARENT,
                                color={ft.ControlState.DEFAULT: TEXT_COLOR},
                            )
                        ),
                        padding=0,
                        alignment=ft.alignment.center,
                        width=45,
                        height=30,
                    ),
                ],
                spacing=0,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
            ),
        )

        download_content = ft.Container(
            content=ft.Column(
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                controls=[
                    ft.Container(
                        content=ft.Icon(
                            ft.Icons.REFRESH,
                            color=ACCENT_COLOR,
                            size=28
                        ),
                        padding=ft.padding.only(bottom=4)
                    ),
                    ft.Container(
                        content=ft.Text(
                            "CHECKING & UPDATING ASSETS",
                            color=ACCENT_COLOR,
                            size=TITLE_SIZE,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER
                        ),
                        padding=ft.padding.only(bottom=15)
                    ),
                    ft.Container(
                        content=self.status_text,
                        padding=ft.padding.only(bottom=15)
                    ),
                    ft.Container(
                        content=self.progress_bar,
                        padding=ft.padding.symmetric(horizontal=10)
                    ),
                ],
                spacing=0
            ),
            padding=0,
            expand=True,
            border_radius=6,
            margin=ft.margin.all(13),
        )

        main_content = ft.Column(
            controls=[
                custom_title_bar,
                ft.Divider(height=1, color=BORDER_COLOR),
                download_content
            ],
            spacing=0,
            expand=True,
        )
        
        self.page.controls.append(main_content)
        self.page.update()

    def update_status(self, message, progress=None):
        self.status_text.value = message
        if progress is not None:
            self.progress_bar.value = progress
        self.page.update()

    def run_download(self):
            try:
                self.update_status("Checking assets...", 0.1)
                manager = UpdateManager()

                self.update_status("Updating files...", 0.3)
                manager.update_files()
                
                self.update_status("Starting...", 1.0)
                time.sleep(1)
                
                self.page.window.close()

            except Exception as e:
                self.update_status(f"Error occurred: {str(e)}", 1.0)
                time.sleep(2)
                self.page.window.close()

def main(page: ft.Page):
    page.title = NAME
    page.bgcolor = MAIN_BG
    page.padding = 0
    page.window.frameless = True
    page.window.width = 350
    page.window.height = 400
    page.window.minimizable = False
    page.window.maximizable = False
    page.window.resizable = False
    page.update()
    
    def start_downloader():
        time.sleep(1)
        downloader = FileDownloader(page)
        downloader.run_download()

    threading.Thread(target=start_downloader, daemon=True).start()

if __name__ == "__main__":
    import subprocess
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    ft.app(target=main, view=ft.AppView.FLET_APP, assets_dir="assets")