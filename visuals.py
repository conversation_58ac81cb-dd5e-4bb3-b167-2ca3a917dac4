import flet as ft
import win32api
import win32con
import win32gui
import json
import os
from pathlib import Path
import threading
import time
from DefaultSettings import *
from Crosshair import Crosshair


def create_crosshair_widget(page: ft.Page, crosshair: Crosshair):
    widget_width = 350
    widget_margin = ft.margin.only(left=-15, right=0, top=-12, bottom=-12)
    toggle_switch_value = False
    selected_color = "#FF0000FF"

    def get_translated_text(key):
        return get_default_text(key)

    def get_default_text(key):
        defaults = {
            "title": "Crosshair",
            "size": "Size",
            "thickness": "Thickness",
            "type": "Crosshair Type",
            "color": "Color",
            "reset_tooltip": "Reset to default"
        }
        return defaults.get(key, "")

    def create_dropdown(label, options, current_value, on_change, width=180):
        dropdown_visible = False
        selected_value = [current_value]
        
        def toggle_dropdown(e):
            nonlocal dropdown_visible
            dropdown_visible = not dropdown_visible
            dropdown_container.visible = dropdown_visible
            page.update()
        
        def select_option(e, value):
            selected_value[0] = value
            selected_text.value = options[value]
            dropdown_container.visible = False
            on_change(value)
            page.update()

        def on_hover(e):
            e.control.bgcolor = ft.colors.with_opacity(0.1, WIDGET_BG) if e.data == "true" else WIDGET_BG
            e.control.update()

        # Dropdown Items
        dropdown_items = ft.Column(
            [
                ft.Container(
                    content=ft.Container(
                        content=ft.Text(text, color=TEXT_COLOR, size=13),
                        padding=8,
                    ),
                    bgcolor=WIDGET_BG,
                    border_radius=4,
                    on_click=lambda e, val=val: select_option(e, val),
                    ink=True,
                    width=width,
                    on_hover=on_hover
                ) for val, text in options.items()
            ],
            spacing=1,
        )
        
        dropdown_container = ft.Container(
            content=dropdown_items,
            bgcolor=WIDGET_BG,
            border_radius=4,
            padding=2,
            visible=False,
            border=ft.border.all(1, BORDER_COLOR),
        )
        
        selected_text = ft.Text(options[current_value], color=TEXT_COLOR, size=12)
        
        main_button = ft.Container(
            content=ft.Row(
                [
                    ft.Container(
                        content=selected_text,
                        expand=True,
                        padding=ft.padding.only(left=8)
                    ),
                    ft.Container(
                        content=ft.Text("▼", color=TEXT_COLOR, size=11),
                        padding=ft.padding.only(right=8)
                    )
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN
            ),
            bgcolor=WIDGET_BG,
            border_radius=4,
            on_click=toggle_dropdown,
            ink=True,
            height=32,
            border=ft.border.all(1, BORDER_COLOR),
            on_hover=on_hover
        )
        
        container = ft.Container(
            content=ft.Column([
                ft.Text(label, size=TEXT_SIZE, color=TEXT_COLOR),
                ft.Container(height=3),
                main_button,
                dropdown_container
            ], spacing=2),
        )
        
        container.selected_value = selected_value
        container.get_selected_value = lambda: selected_value[0]
        
        return container

    def create_color_dropdown(label, current_color, on_change, width=180):
        dropdown_visible = False
        
        color_options = {
            "Red": "#FF0000FF",
            "Green": "#00FF00FF",
            "Blue": "#0000FFFF",
            "Yellow": "#FFFF00FF",
            "Purple": "#800080FF",
            "White": "#FFFFFFFF",
        }
        
        def toggle_dropdown(e):
            nonlocal dropdown_visible
            dropdown_visible = not dropdown_visible
            dropdown_container.visible = dropdown_visible
            page.update()
        
        def select_color(e, color_hex):
            selected_color_box.bgcolor = color_hex[:7]
            dropdown_container.visible = False
            on_change(color_hex)
            page.update()

        def on_hover(e):
            e.control.bgcolor = ft.colors.with_opacity(0.1, WIDGET_BG) if e.data == "true" else WIDGET_BG
            e.control.update()

        scrollable_content = ft.Column(
            [
                ft.Container(
                    content=ft.Container(
                        content=ft.Row([
                            ft.Container(
                                bgcolor=color_hex[:7],
                                width=15,
                                height=15,
                                border_radius=4,
                                border=ft.border.all(1, BORDER_COLOR),
                            ),
                            ft.Container(width=8),
                            ft.Text(name, color=TEXT_COLOR, size=13),
                        ]),
                        padding=8,
                    ),
                    bgcolor=WIDGET_BG,
                    border_radius=4,
                    on_click=lambda e, hex=color_hex: select_color(e, hex),
                    ink=True,
                    width=width,
                    on_hover=on_hover
                ) for name, color_hex in color_options.items()
            ],
            spacing=1,
            scroll=ft.ScrollMode.AUTO,
            height=150,
        )
        
        dropdown_container = ft.Container(
            content=scrollable_content,
            bgcolor=WIDGET_BG,
            border_radius=4,
            padding=2,
            visible=False,
            border=ft.border.all(1, BORDER_COLOR),
        )
        
        selected_color_box = ft.Container(
            bgcolor=current_color[:7],
            width=15,
            height=15,
            border_radius=4,
            border=ft.border.all(1, BORDER_COLOR),
        )
        
        main_button = ft.Container(
            content=ft.Row(
                [
                    ft.Container(
                        content=ft.Row([
                            selected_color_box,
                            ft.Container(width=8),
                        ]),
                        expand=True,
                        padding=ft.padding.only(left=8)
                    ),
                    ft.Container(
                        content=ft.Text("▼", color=TEXT_COLOR, size=11),
                        padding=ft.padding.only(right=8)
                    )
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN
            ),
            bgcolor=WIDGET_BG,
            border_radius=4,
            on_click=toggle_dropdown,
            ink=True,
            height=32,
            border=ft.border.all(1, BORDER_COLOR),
            on_hover=on_hover
        )
        
        container = ft.Container(
            content=ft.Column([
                ft.Text(label, size=TEXT_SIZE, color=TEXT_COLOR),
                ft.Container(height=3),
                main_button,
                dropdown_container
            ], spacing=2),
        )
        
        return container

    def create_labeled_slider(label, min_val=0, max_val=50, default=50):
        value_display = ft.TextField(
            value=str(int(default)),
            width=42,
            height=22,
            text_size=TEXT_SIZE,
            color=ACCENT_COLOR,
            border_color=ft.colors.TRANSPARENT,
            focused_border_color=ACCENT_COLOR,
            content_padding=ft.padding.all(3),
            text_align=ft.TextAlign.CENTER,
            keyboard_type=ft.KeyboardType.NUMBER,
            border_radius=4,
            bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
        )

        def validate_and_update_value(new_value):
            try:
                num_value = max(min_val, min(max_val, int(float(new_value or 0))))
                slider.value = num_value
                value_display.value = str(num_value)
                
                crosshair.update_settings(
                    enabled=toggle_switch_value,
                    size=int(size_slider.slider.value),
                    thickness=int(thickness_slider.slider.value),
                    type=type_selector.get_selected_value().lower(),
                    color=selected_color
                )
                value_display.update()
                slider.update()
            except Exception as e:
                print(f"Error in validate_and_update_value: {e}")

        def reset_value(e):
            slider.value = default
            value_display.value = str(default)
            
            crosshair.update_settings(
                enabled=toggle_switch_value,
                size=int(size_slider.slider.value),
                thickness=int(thickness_slider.slider.value),
                type=type_selector.get_selected_value().lower(),
                color=selected_color
            )
            
            reset_button.content.color = ACCENT_COLOR
            reset_button.update()
            
            def color_reset_thread():
                time.sleep(0.5)
                reset_button.content.color = ft.colors.YELLOW
                reset_button.update()
            
            threading.Thread(target=color_reset_thread).start()
            
            value_display.update()
            slider.update()

        reset_button = ft.Container(
            content=ft.Icon(
                ft.icons.RESTART_ALT_ROUNDED,
                color=ft.colors.YELLOW,
                size=20,
            ),
            width=24,
            height=24,
            border_radius=4,
            on_click=reset_value,
            tooltip=get_translated_text("reset_tooltip", current_language),
            ink=False,
        )

        slider = ft.Slider(
            min=min_val,
            max=max_val,
            value=default,
            active_color=ACCENT_COLOR,
            inactive_color=ft.colors.with_opacity(0.1, ACCENT_COLOR),
            expand=True,
            height=12,
            width=420,
            divisions=max_val - min_val,
            thumb_color=ACCENT_COLOR,
            on_change=lambda e: validate_and_update_value(str(int(e.control.value))),
        )

        container = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(label, size=TEXT_SIZE, color=TEXT_COLOR),
                    ft.Container(expand=True),
                    value_display,
                ]),
                ft.Container(height=1),
                ft.Row([
                    ft.Container(
                        content=slider,
                        padding=ft.padding.only(left=-20, right=15),
                        expand=True,
                    ),
                    reset_button,
                ]),
            ], spacing=0),
            padding=ft.padding.only(left=0, top=2, bottom=2),
        )
        
        setattr(container, 'slider', slider)
        setattr(container, 'value_display', value_display)
        
        return container

    def toggle_switch_clicked(e):
        nonlocal toggle_switch_value
        toggle_switch_value = not toggle_switch_value
        toggle_switch.controls[1].right = 0 if toggle_switch_value else 20
        toggle_switch.controls[1].bgcolor = ACCENT_COLOR if toggle_switch_value else ft.colors.GREY_400
        toggle_switch.update()

        crosshair.update_settings(
            enabled=toggle_switch_value,
            size=int(size_slider.slider.value),
            thickness=int(thickness_slider.slider.value),
            type=type_selector.get_selected_value().lower(),
            color=selected_color
        )

    toggle_switch = ft.Stack(
        controls=[
            ft.Container(
                width=45,
                height=18,
                border_radius=12,
                bgcolor=ft.colors.with_opacity(0.1, ACCENT_COLOR),
                animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
            ),
            ft.Container(
                width=18,
                height=18,
                right=20,
                bgcolor=ft.colors.GREY_400,
                border_radius=12,
                animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                on_click=lambda e: toggle_switch_clicked(e),
            )
        ]
    )

    size_slider = create_labeled_slider(get_translated_text("size", current_language), min_val=1, max_val=20, default=15)
    thickness_slider = create_labeled_slider(get_translated_text("thickness", current_language), min_val=1, max_val=10, default=3)
    
    type_selector = create_dropdown(
        get_translated_text("type", current_language),
        {"Cross": "CROSS", "Dot": "DOT", "X": "X"},
        "Cross",
        lambda type_name: crosshair.update_settings(
            enabled=toggle_switch_value,
            size=int(size_slider.slider.value),
            thickness=int(thickness_slider.slider.value),
            type=type_name.lower(),  # Hier verwenden wir direkt type_name
            color=selected_color
        )
    )

    color_selector = create_color_dropdown(
        get_translated_text("color", current_language),  # Wird nun "Crosshair Color" etc.
        selected_color,
        lambda color: crosshair.update_settings(
            enabled=toggle_switch_value,
            size=int(size_slider.slider.value),
            thickness=int(thickness_slider.slider.value),
            type=type_selector.get_selected_value().lower(),
            color=color
        )
    )

    container = ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.CONTROL_CAMERA, color=ACCENT_COLOR, size=TITLE_SIZE),
                    ft.Text(get_translated_text("title", current_language), size=TITLE_SIZE, weight=ft.FontWeight.BOLD, color=ACCENT_COLOR),
                    ft.Container(expand=True),
                    ft.Container(content=toggle_switch, padding=ft.padding.only(top=2)),
                ], alignment=ft.MainAxisAlignment.START),
                padding=ft.padding.only(left=8, right=8)
            ),
            ft.Container(ft.Divider(color=ft.colors.with_opacity(0.1, ACCENT_COLOR), height=1)),
            ft.Container(
                content=ft.Column([
                    size_slider,
                    thickness_slider,
                    type_selector,
                    color_selector,
                ], spacing=10),
                padding=ft.padding.symmetric(horizontal=12),
            ),
        ], spacing=5),
        padding=ft.padding.only(left=4, top=12, bottom=12, right=0),
        margin=widget_margin,
        bgcolor=WIDGET_BG,
        border_radius=4,
        border=ft.border.all(1, BORDER_COLOR),
        width=widget_width,
    )

    def update_translations(new_language):
        container.content.controls[0].content.controls[1].value = get_translated_text("title", new_language)
        size_slider.content.controls[0].controls[0].value = get_translated_text("size", new_language)
        thickness_slider.content.controls[0].controls[0].value = get_translated_text("thickness", new_language)
        type_selector.content.controls[0].value = get_translated_text("type", new_language)
        color_selector.content.controls[0].value = get_translated_text("color", new_language)
        if container in page.controls:
            container.update()

    def get_color_code(name):
        color_map = {
            "Red": "#FF0000FF",
            "Green": "#00FF00FF",
            "Blue": "#0000FFFF",
            "Yellow": "#FFFF00FF",
            "Purple": "#800080FF",
            "White": "#FFFFFFFF"
        }
        return color_map.get(name)

    # In der load_settings Funktion in create_crosshair_widget:
    def load_settings(settings):
        if settings:
            nonlocal toggle_switch_value, selected_color
            
            # Toggle Switch
            toggle_switch_value = settings.get('enabled', False)
            toggle_switch.controls[1].right = 0 if toggle_switch_value else 20
            toggle_switch.controls[1].bgcolor = ACCENT_COLOR if toggle_switch_value else ft.colors.GREY_400
            
            # Slider
            if 'size' in settings:
                size_slider.slider.value = int(float(settings['size']))
                size_slider.value_display.value = str(int(float(settings['size'])))
                
            if 'thickness' in settings:
                thickness_slider.slider.value = int(float(settings['thickness']))
                thickness_slider.value_display.value = str(int(float(settings['thickness'])))
                
            # Type Selector
            if "type" in settings:
                type_name = settings["type"]
                type_selector.selected_value[0] = type_name
                type_button_text = type_selector.content.controls[2].content.controls[0].content
                
                my_options = {"Cross": "CROSS", "Dot": "DOT", "X": "X"}
                for k, v in my_options.items():
                    if v.lower() == type_name.lower():
                        type_button_text.value = v
                        break
            
            # Color Selector
            if 'color' in settings:
                selected_color = settings['color']
                color_button = color_selector.content.controls[2]
                color_box = color_button.content.controls[0].content.controls[0]
                color_box.bgcolor = selected_color[:7]
            
            crosshair.update_settings(
                enabled=toggle_switch_value,
                size=int(size_slider.slider.value),
                thickness=int(thickness_slider.slider.value),
                type=type_selector.get_selected_value().lower(),
                color=selected_color
            )
            
            page.update()

    setattr(container, 'load_settings', load_settings)
    
    setattr(container, 'update_translations', update_translations)
    setattr(container, 'get_settings', lambda: {
        'enabled': toggle_switch_value,
        'size': int(size_slider.slider.value),
        'thickness': int(thickness_slider.slider.value),
        'type': type_selector.get_selected_value(),
        'color': selected_color
    })

    return container




def create_visual_layout(page: ft.Page, window_manager):
    # Nur initialisieren, nicht starten
    crosshair = Crosshair()
    crosshair_widget = create_crosshair_widget(page, crosshair)
    page.crosshair_widget = crosshair_widget
    
    layout = ft.Container(
        content=ft.Stack(
            controls=[
                crosshair_widget,
            ],
        ),
    )
    
    # Crosshair dem Page-Objekt zuweisen für späteren Start
    page.crosshair = crosshair
    
    return layout